#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Basic import test to verify the relationship functionality can be imported.
"""
import sys
import os

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that all new relationship functionality can be imported."""
    try:
        print("Testing basic imports...")
        
        # Test basic CRUD import
        from sqlalchemy_crud_plus import CRUDPlus
        print("✅ CRUDPlus imported successfully")
        
        # Test error imports
        from sqlalchemy_crud_plus.errors import RelationshipError
        print("✅ RelationshipError imported successfully")
        
        # Test utility function imports
        from sqlalchemy_crud_plus.utils import (
            parse_relationship_options,
            get_relationship_attribute,
            get_relationship_target_model,
            validate_relationship_data,
            build_relationship_filters
        )
        print("✅ All relationship utility functions imported successfully")
        
        print("\n🎉 All imports successful! The relationship functionality is properly integrated.")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False


def test_crud_methods():
    """Test that CRUDPlus has the new relationship methods."""
    try:
        print("\nTesting CRUDPlus relationship methods...")
        
        from sqlalchemy_crud_plus import CRUDPlus
        
        # Check if new methods exist
        relationship_methods = [
            'select_model_with_relationships',
            'select_model_by_column_with_relationships', 
            'select_models_with_relationships',
            'create_model_with_relationships',
            'update_model_with_relationships',
            'add_relationship',
            'remove_relationship'
        ]
        
        for method_name in relationship_methods:
            if hasattr(CRUDPlus, method_name):
                print(f"✅ {method_name} method exists")
            else:
                print(f"❌ {method_name} method missing")
                return False
        
        print("\n🎉 All relationship methods are available!")
        return True
        
    except Exception as e:
        print(f"❌ Error checking methods: {e}")
        return False


def main():
    """Run all basic tests."""
    print("🚀 Testing SQLAlchemy CRUD Plus Relationship Integration")
    print("=" * 60)
    
    import_success = test_imports()
    method_success = test_crud_methods()
    
    print("\n" + "=" * 60)
    if import_success and method_success:
        print("🎉 All basic tests passed! Relationship functionality is properly integrated.")
        return 0
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
