#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import warnings
from typing import Any, Callable

from sqlalchemy import (
    ColumnElement, Select, and_, asc, desc, or_, select, inspect, text
)
from sqlalchemy.orm import (
    InstrumentedAttribute, RelationshipProperty, selectinload, joinedload,
    subqueryload, contains_eager, defer, undefer, load_only, raiseload
)
from sqlalchemy.orm.util import AliasedClass

from sqlalchemy_crud_plus.errors import (
    ColumnSortError, ModelColumnError, SelectOperatorError, RelationshipError
)
from sqlalchemy_crud_plus.types import Model

_SUPPORTED_FILTERS = {
    # Comparison: https://docs.sqlalchemy.org/en/20/core/operators.html#comparison-operators
    'gt': lambda column: column.__gt__,
    'lt': lambda column: column.__lt__,
    'ge': lambda column: column.__ge__,
    'le': lambda column: column.__le__,
    'eq': lambda column: column.__eq__,
    'ne': lambda column: column.__ne__,
    'between': lambda column: column.between,
    # IN: https://docs.sqlalchemy.org/en/20/core/operators.html#in-comparisons
    'in': lambda column: column.in_,
    'not_in': lambda column: column.not_in,
    # Identity: https://docs.sqlalchemy.org/en/20/core/operators.html#identity-comparisons
    'is': lambda column: column.is_,
    'is_not': lambda column: column.is_not,
    'is_distinct_from': lambda column: column.is_distinct_from,
    'is_not_distinct_from': lambda column: column.is_not_distinct_from,
    # String: https://docs.sqlalchemy.org/en/20/core/operators.html#string-comparisons
    'like': lambda column: column.like,
    'not_like': lambda column: column.not_like,
    'ilike': lambda column: column.ilike,
    'not_ilike': lambda column: column.not_ilike,
    # String Containment: https://docs.sqlalchemy.org/en/20/core/operators.html#string-containment
    'startswith': lambda column: column.startswith,
    'endswith': lambda column: column.endswith,
    'contains': lambda column: column.contains,
    # String matching: https://docs.sqlalchemy.org/en/20/core/operators.html#string-matching
    'match': lambda column: column.match,
    # String Alteration: https://docs.sqlalchemy.org/en/20/core/operators.html#string-alteration
    'concat': lambda column: column.concat,
    # Arithmetic: https://docs.sqlalchemy.org/en/20/core/operators.html#arithmetic-operators
    'add': lambda column: column.__add__,
    'radd': lambda column: column.__radd__,
    'sub': lambda column: column.__sub__,
    'rsub': lambda column: column.__rsub__,
    'mul': lambda column: column.__mul__,
    'rmul': lambda column: column.__rmul__,
    'truediv': lambda column: column.__truediv__,
    'rtruediv': lambda column: column.__rtruediv__,
    'floordiv': lambda column: column.__floordiv__,
    'rfloordiv': lambda column: column.__rfloordiv__,
    'mod': lambda column: column.__mod__,
    'rmod': lambda column: column.__rmod__,
}

_DYNAMIC_OPERATORS = [
    'concat',
    'add',
    'radd',
    'sub',
    'rsub',
    'mul',
    'rmul',
    'truediv',
    'rtruediv',
    'floordiv',
    'rfloordiv',
    'mod',
    'rmod',
]


def get_sqlalchemy_filter(
    operator: str, value: Any, allow_arithmetic: bool = True
) -> Callable[[InstrumentedAttribute], Callable] | None:
    if operator in ['in', 'not_in', 'between']:
        if not isinstance(value, (tuple, list, set)):
            raise SelectOperatorError(f'The value of the <{operator}> filter must be tuple, list or set')

    if operator in _DYNAMIC_OPERATORS and not allow_arithmetic:
        raise SelectOperatorError(f'Nested arithmetic operations are not allowed: {operator}')

    sqlalchemy_filter = _SUPPORTED_FILTERS.get(operator)
    if sqlalchemy_filter is None and operator != 'or':
        warnings.warn(
            f'The operator <{operator}> is not yet supported, only {", ".join(_SUPPORTED_FILTERS.keys())}.',
            SyntaxWarning,
        )
        return None

    return sqlalchemy_filter


def get_column(model: type[Model] | AliasedClass, field_name: str) -> InstrumentedAttribute:
    """
    Get column from model with validation.

    :param model: The SQLAlchemy model class or aliased class
    :param field_name: The column name to retrieve
    :return:
    """
    column = getattr(model, field_name, None)
    if column is None:
        raise ModelColumnError(f'Column {field_name} is not found in {model}')

    if hasattr(model, '__table__') and hasattr(column, 'property'):
        if not hasattr(column.property, 'columns'):
            raise ModelColumnError(f'{field_name} is not a valid column in {model}')

    return column


def _create_or_filters(column: InstrumentedAttribute, op: str, value: dict[str, Any]) -> list[ColumnElement | None]:
    """
    Create OR filter expressions.

    :param column: The SQLAlchemy column
    :param op: The operator (should be 'or')
    :param value: Dictionary of operator-value pairs
    :return:
    """
    or_filters = []
    if op == 'or':
        for or_op, or_value in value.items():
            sqlalchemy_filter = get_sqlalchemy_filter(or_op, or_value)
            if sqlalchemy_filter is not None:
                or_filters.append(sqlalchemy_filter(column)(or_value))
    return or_filters


def _create_arithmetic_filters(
    column: InstrumentedAttribute, op: str, value: dict[str, Any]
) -> list[ColumnElement | None]:
    """
    Create arithmetic filter expressions.

    :param column: The SQLAlchemy column
    :param op: The arithmetic operator
    :param value: Dictionary containing 'value' and 'condition' keys
    :return:
    """
    arithmetic_filters = []
    if isinstance(value, dict) and {'value', 'condition'}.issubset(value):
        arithmetic_value = value['value']
        condition = value['condition']
        sqlalchemy_filter = get_sqlalchemy_filter(op, arithmetic_value)
        if sqlalchemy_filter is not None:
            for cond_op, cond_value in condition.items():
                arithmetic_filter = get_sqlalchemy_filter(cond_op, cond_value, allow_arithmetic=False)
                if arithmetic_filter is not None:
                    arithmetic_filters.append(
                        arithmetic_filter(sqlalchemy_filter(column)(arithmetic_value))(cond_value)
                        if cond_op != 'between'
                        else arithmetic_filter(sqlalchemy_filter(column)(arithmetic_value))(*cond_value)
                    )
    return arithmetic_filters


def _create_and_filters(column: InstrumentedAttribute, op: str, value: Any) -> list[ColumnElement | None]:
    """
    Create AND filter expressions.

    :param column: The SQLAlchemy column
    :param op: The filter operator
    :param value: The filter value
    :return:
    """
    and_filters = []
    sqlalchemy_filter = get_sqlalchemy_filter(op, value)
    if sqlalchemy_filter is not None:
        and_filters.append(sqlalchemy_filter(column)(value) if op != 'between' else sqlalchemy_filter(column)(*value))
    return and_filters


def parse_filters(model: type[Model] | AliasedClass, **kwargs) -> list[ColumnElement]:
    """
    Parse filter expressions from keyword arguments.

    :param model: The SQLAlchemy model class or aliased class
    :param kwargs: Filter expressions using field__operator=value syntax
    :return:
    """
    filters = []

    for key, value in kwargs.items():
        if '__' not in key:
            column = get_column(model, key)
            filters.append(column == value)
            continue

        field_name, op = key.rsplit('__', 1)

        # OR GROUP
        if field_name == '__or' and op == '':
            __or__filters = []

            for _key, _value in value.items():
                if '__' not in _key:
                    _column = get_column(model, _key)
                    if isinstance(_value, list):
                        for single_value in _value:
                            __or__filters.append(_column == single_value)
                    else:
                        __or__filters.append(_column == _value)
                else:
                    _field_name, _op = _key.rsplit('__', 1)
                    _column = get_column(model, _field_name)

                    if isinstance(_value, list) and _op not in ['in', 'not_in', 'between']:
                        for single_value in _value:
                            __or__filters.extend(_create_and_filters(_column, _op, single_value))
                    else:
                        if _op == 'or':
                            __or__filters.extend(_create_or_filters(_column, _op, _value))
                        elif _op in _DYNAMIC_OPERATORS:
                            __or__filters.extend(_create_arithmetic_filters(_column, _op, _value))
                        else:
                            __or__filters.extend(_create_and_filters(_column, _op, _value))

            if __or__filters:
                filters.append(or_(*__or__filters))
        else:
            column = get_column(model, field_name)

            if op == 'or':
                filters.append(or_(*_create_or_filters(column, op, value)))
            elif op in _DYNAMIC_OPERATORS:
                arithmetic_filters = _create_arithmetic_filters(column, op, value)
                if arithmetic_filters:
                    filters.append(and_(*arithmetic_filters))
            else:
                filters.extend(_create_and_filters(column, op, value))

    return filters


def apply_sorting(
    model: type[Model] | AliasedClass,
    stmt: Select,
    sort_columns: str | list[str],
    sort_orders: str | list[str] | None = None,
) -> Select:
    """
    Apply sorting to a SQLAlchemy query based on specified column names and sort orders.

    :param model: The SQLAlchemy model
    :param stmt: The SQLAlchemy Select statement to which sorting will be applied
    :param sort_columns: Column name or list of column names to sort by
    :param sort_orders: Sort order ("asc" or "desc") or list of sort orders
    :return:
    """
    if sort_orders and not sort_columns:
        raise ValueError('Sort orders provided without corresponding sort columns.')

    if sort_columns:
        if not isinstance(sort_columns, list):
            sort_columns = [sort_columns]

        if sort_orders:
            if not isinstance(sort_orders, list):
                sort_orders = [sort_orders] * len(sort_columns)

            if len(sort_columns) != len(sort_orders):
                raise ColumnSortError('The length of sort_columns and sort_orders must match.')

            for order in sort_orders:
                if order not in ['asc', 'desc']:
                    raise SelectOperatorError(
                        f'Select sort operator {order} is not supported, only supports `asc`, `desc`'
                    )

        validated_sort_orders = ['asc'] * len(sort_columns) if not sort_orders else sort_orders

        for idx, column_name in enumerate(sort_columns):
            column = get_column(model, column_name)
            order = validated_sort_orders[idx]
            stmt = stmt.order_by(asc(column) if order == 'asc' else desc(column))

    return stmt


def apply_options(stmt: Select, options: list[Any] | None = None) -> Select:
    """
    Apply SQLAlchemy options to a query statement.

    :param stmt: The SQLAlchemy Select statement
    :param options: List of SQLAlchemy options (loading strategies, etc.)
    :return: Statement with applied options
    """
    if options:
        for option in options:
            stmt = stmt.options(option)
    return stmt


def parse_relationship_filters(model: type[Model] | AliasedClass, **kwargs) -> tuple[dict[str, Any], dict[str, Any]]:
    """
    Separate relationship filters from regular filters.

    :param model: The SQLAlchemy model class or aliased class
    :param kwargs: Filter expressions
    :return: Tuple of (regular_filters, relationship_filters)
    """
    regular_filters = {}
    relationship_filters = {}

    for key, value in kwargs.items():
        if '__' in key:
            # Check if this might be a relationship filter
            first_part = key.split('__')[0]
            if hasattr(model, first_part):
                attr = getattr(model, first_part)
                if hasattr(attr, 'property') and isinstance(attr.property, RelationshipProperty):
                    relationship_filters[key] = value
                    continue
        regular_filters[key] = value

    return regular_filters, relationship_filters


def get_relationship_attribute(model: type[Model], relationship_name: str) -> InstrumentedAttribute:
    """
    Get relationship attribute from model with validation.

    :param model: The SQLAlchemy model class
    :param relationship_name: The relationship name to retrieve
    :return: The relationship attribute
    """
    if not hasattr(model, relationship_name):
        raise RelationshipError(f'Relationship {relationship_name} not found in {model.__name__}')

    attr = getattr(model, relationship_name)
    if not hasattr(attr, 'property') or not isinstance(attr.property, RelationshipProperty):
        raise RelationshipError(f'{relationship_name} is not a valid relationship in {model.__name__}')

    return attr


def get_relationship_target_model(model: type[Model], relationship_name: str) -> type[Model]:
    """
    Get the target model of a relationship.

    :param model: The SQLAlchemy model class
    :param relationship_name: The relationship name
    :return: The target model class
    """
    relationship_attr = get_relationship_attribute(model, relationship_name)
    return relationship_attr.property.mapper.class_


def build_relationship_filters(
    model: type[Model] | AliasedClass,
    relationship_filters: dict[str, Any]
) -> list[ColumnElement]:
    """
    Build filters for querying across relationships.

    :param model: The SQLAlchemy model class or aliased class
    :param relationship_filters: Dictionary of relationship filters
    :return: List of filter expressions
    """
    filters = []

    for filter_key, filter_value in relationship_filters.items():
        parts = filter_key.split('__')
        if len(parts) < 2:
            continue

        relationship_name = parts[0]
        field_parts = parts[1:]

        try:
            relationship_attr = getattr(model, relationship_name)
            if not hasattr(relationship_attr, 'property'):
                continue

            if not isinstance(relationship_attr.property, RelationshipProperty):
                continue

            # Get target model
            target_model = relationship_attr.property.mapper.class_

            # Build the field filter for the related model
            if len(field_parts) == 1:
                # Simple equality: relationship__field
                field_name = field_parts[0]
                target_column = get_column(target_model, field_name)

                # Use has() for single relationships, any() for collections
                if relationship_attr.property.uselist:
                    filters.append(relationship_attr.any(target_column == filter_value))
                else:
                    filters.append(relationship_attr.has(target_column == filter_value))

            elif len(field_parts) == 2:
                # With operator: relationship__field__operator
                field_name, operator = field_parts
                target_column = get_column(target_model, field_name)

                sqlalchemy_filter = get_sqlalchemy_filter(operator, filter_value)
                if sqlalchemy_filter is not None:
                    condition = (
                        sqlalchemy_filter(target_column)(filter_value)
                        if operator != 'between'
                        else sqlalchemy_filter(target_column)(*filter_value)
                    )

                    # Use has() for single relationships, any() for collections
                    if relationship_attr.property.uselist:
                        filters.append(relationship_attr.any(condition))
                    else:
                        filters.append(relationship_attr.has(condition))

        except Exception:
            # Skip invalid relationship filters
            continue

    return filters


# ==================== Advanced Query Builder ====================

class QueryBuilder:
    """
    Advanced query builder for SQLAlchemy with comprehensive relationship support.
    """

    def __init__(self, model: type[Model]):
        self.model = model
        self.stmt: Select | None = None

    def build_base_query(self, *whereclause, **kwargs) -> Select:
        """
        Build base query with filters.

        :param whereclause: Additional WHERE clauses
        :param kwargs: Filter expressions
        :return: Base SELECT statement
        """
        # Separate relationship filters from regular filters
        regular_filters, relationship_filters = parse_relationship_filters(self.model, **kwargs)

        # Build base statement
        self.stmt = select(self.model)

        # Apply regular filters
        filters = parse_filters(self.model, **regular_filters) + list(whereclause)

        # Apply relationship filters
        if relationship_filters:
            filters.extend(build_relationship_filters(self.model, relationship_filters))

        if filters:
            self.stmt = self.stmt.where(*filters)

        return self.stmt

    def apply_joins(self, joins: list[dict] | None = None) -> Select:
        """
        Apply joins to the query.

        :param joins: List of join configurations
            Format: [
                {
                    'target': 'relationship_name' or Model,
                    'type': 'inner' | 'left' | 'right' | 'full' | 'cross',
                    'onclause': optional join condition,
                    'isouter': bool (for backward compatibility)
                }
            ]
        :return: Statement with applied joins
        """
        if not joins or self.stmt is None:
            return self.stmt

        for join_config in joins:
            target = join_config.get('target')
            join_type = join_config.get('type', 'inner').lower()
            onclause = join_config.get('onclause')
            isouter = join_config.get('isouter', False)

            if isinstance(target, str):
                # Join by relationship name
                relationship_attr = get_relationship_attribute(self.model, target)
                target_model = relationship_attr.property.mapper.class_

                # Apply join based on type
                if join_type == 'inner' and not isouter:
                    self.stmt = self.stmt.join(target_model, onclause) if onclause else self.stmt.join(relationship_attr)
                elif join_type in ['left', 'outer'] or isouter:
                    self.stmt = self.stmt.outerjoin(target_model, onclause) if onclause else self.stmt.outerjoin(relationship_attr)
                elif join_type == 'right':
                    # SQLAlchemy doesn't have direct right join, simulate with left join by swapping
                    if onclause:
                        self.stmt = self.stmt.join(target_model, onclause, isouter=True)
                    else:
                        self.stmt = self.stmt.outerjoin(relationship_attr)
                elif join_type == 'full':
                    # Full outer join
                    self.stmt = self.stmt.outerjoin(target_model, onclause, full=True) if onclause else self.stmt.outerjoin(relationship_attr)
                elif join_type == 'cross':
                    # Cross join (Cartesian product)
                    if hasattr(self.stmt, 'join'):
                        self.stmt = self.stmt.join(target_model, text('1=1'))
            else:
                # Direct model join
                if join_type == 'inner' and not isouter:
                    self.stmt = self.stmt.join(target, onclause)
                elif join_type in ['left', 'outer'] or isouter:
                    self.stmt = self.stmt.outerjoin(target, onclause)
                elif join_type == 'cross':
                    self.stmt = self.stmt.join(target, text('1=1'))

        return self.stmt

    def apply_options(self, options: list[Any] | None = None) -> Select:
        """
        Apply SQLAlchemy options (loading strategies, etc.).

        :param options: List of SQLAlchemy options
        :return: Statement with applied options
        """
        if options and self.stmt is not None:
            for option in options:
                self.stmt = self.stmt.options(option)
        return self.stmt

    def apply_sorting(self, sort_columns: str | list[str] | None = None,
                     sort_orders: str | list[str] | None = None) -> Select:
        """
        Apply sorting to the query.

        :param sort_columns: Column name(s) to sort by
        :param sort_orders: Sort order(s) ('asc' or 'desc')
        :return: Statement with applied sorting
        """
        if sort_columns and self.stmt is not None:
            self.stmt = apply_sorting(self.model, self.stmt, sort_columns, sort_orders)
        return self.stmt

    def apply_pagination(self, limit: int | None = None, offset: int | None = None) -> Select:
        """
        Apply pagination to the query.

        :param limit: Maximum number of results to return
        :param offset: Number of results to skip
        :return: Statement with applied pagination
        """
        if self.stmt is not None:
            if limit is not None:
                self.stmt = self.stmt.limit(limit)
            if offset is not None:
                self.stmt = self.stmt.offset(offset)
        return self.stmt


def build_advanced_query(
    model: type[Model],
    *whereclause,
    joins: list[dict] | None = None,
    options: list[Any] | None = None,
    sort_columns: str | list[str] | None = None,
    sort_orders: str | list[str] | None = None,
    limit: int | None = None,
    offset: int | None = None,
    **kwargs
) -> Select:
    """
    Build advanced query with comprehensive relationship support.

    :param model: The SQLAlchemy model class
    :param whereclause: Additional WHERE clauses
    :param joins: List of join configurations
    :param options: List of SQLAlchemy options
    :param sort_columns: Columns to sort by
    :param sort_orders: Sort orders
    :param limit: Maximum number of results to return
    :param offset: Number of results to skip
    :param kwargs: Filter expressions
    :return: SQLAlchemy Select statement
    """
    builder = QueryBuilder(model)

    # Build base query with filters
    stmt = builder.build_base_query(*whereclause, **kwargs)

    # Apply joins
    stmt = builder.apply_joins(joins)

    # Apply options
    stmt = builder.apply_options(options)

    # Apply sorting
    stmt = builder.apply_sorting(sort_columns, sort_orders)

    # Apply pagination
    stmt = builder.apply_pagination(limit, offset)

    return stmt


def create_loading_options(
    model: type[Model],
    relationships: str | list[str] | dict[str, Any] | None = None,
    strategy: str = 'selectinload'
) -> list[Any]:
    """
    Create loading options for relationships.

    :param model: The SQLAlchemy model class
    :param relationships: Relationships to load
    :param strategy: Loading strategy
    :return: List of loading options
    """
    if not relationships:
        return []

    strategy_map = {
        'selectinload': selectinload,
        'joinedload': joinedload,
        'subqueryload': subqueryload,
        'contains_eager': contains_eager,
        'defer': defer,
        'undefer': undefer,
        'load_only': load_only,
        'raiseload': raiseload,
    }

    if strategy not in strategy_map:
        raise ValueError(f'Unsupported loading strategy: {strategy}')

    strategy_func = strategy_map[strategy]
    options = []

    if isinstance(relationships, str):
        # Single relationship
        relationship_attr = get_relationship_attribute(model, relationships)
        options.append(strategy_func(relationship_attr))
    elif isinstance(relationships, list):
        # Multiple relationships
        for rel_name in relationships:
            relationship_attr = get_relationship_attribute(model, rel_name)
            options.append(strategy_func(relationship_attr))
    elif isinstance(relationships, dict):
        # Nested relationships with custom strategies
        for rel_name, rel_config in relationships.items():
            relationship_attr = get_relationship_attribute(model, rel_name)

            if isinstance(rel_config, str):
                # Custom strategy for this relationship
                rel_strategy = strategy_map.get(rel_config, strategy_func)
                options.append(rel_strategy(relationship_attr))
            elif isinstance(rel_config, dict):
                # Nested relationship configuration
                rel_strategy = strategy_map.get(rel_config.get('strategy', strategy), strategy_func)
                option = rel_strategy(relationship_attr)

                # Handle nested relationships
                if 'nested' in rel_config:
                    target_model = get_relationship_target_model(model, rel_name)
                    nested_options = create_loading_options(
                        target_model,
                        rel_config['nested'],
                        rel_config.get('nested_strategy', strategy)
                    )
                    for nested_option in nested_options:
                        option = option.options(nested_option)

                options.append(option)
            else:
                # Default strategy
                options.append(strategy_func(relationship_attr))

    return options


def validate_relationship_data(
    model: type[Model],
    relationship_name: str,
    data: Any,
    operation: str = 'create'
) -> bool:
    """
    Validate relationship data for CRUD operations.

    :param model: The SQLAlchemy model class
    :param relationship_name: The relationship name
    :param data: The data to validate
    :param operation: The operation type ('create', 'update', 'delete')
    :return: True if valid, raises exception if invalid
    """
    try:
        relationship_attr = get_relationship_attribute(model, relationship_name)
        target_model = get_relationship_target_model(model, relationship_name)

        # Check if relationship is a collection
        is_collection = relationship_attr.property.uselist

        if operation in ['create', 'update']:
            if is_collection:
                if not isinstance(data, (list, tuple, set)):
                    raise RelationshipError(
                        f'Relationship {relationship_name} expects a collection, got {type(data).__name__}'
                    )
            else:
                if isinstance(data, (list, tuple, set)):
                    raise RelationshipError(
                        f'Relationship {relationship_name} expects a single object, got a collection'
                    )

        return True

    except Exception as e:
        raise RelationshipError(f'Invalid relationship data for {relationship_name}: {str(e)}')


def build_subquery(
    model: type[Model],
    subquery_config: dict[str, Any]
) -> Select:
    """
    Build a subquery for advanced filtering.

    :param model: The SQLAlchemy model class
    :param subquery_config: Subquery configuration
    :return: Subquery Select statement
    """
    subquery_model = subquery_config.get('model', model)
    subquery_filters = subquery_config.get('filters', {})
    subquery_columns = subquery_config.get('columns', [])

    # Build subquery
    if subquery_columns:
        # Select specific columns
        columns = [get_column(subquery_model, col) for col in subquery_columns]
        stmt = select(*columns)
    else:
        # Select all
        stmt = select(subquery_model)

    # Apply filters
    if subquery_filters:
        filters = parse_filters(subquery_model, **subquery_filters)
        if filters:
            stmt = stmt.where(*filters)

    return stmt.subquery()
