#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from typing import Any, Generic, Iterable, Sequence

from sqlalchemy import (
    Column,
    ColumnExpressionArgument,
    Select,
    delete,
    func,
    inspect,
    select,
    update,
)
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload, joinedload, contains_eager, subqueryload
from sqlalchemy.orm.strategy_options import Load

from sqlalchemy_crud_plus.errors import CompositePrimaryKeysError, ModelColumnError, MultipleResultsError
from sqlalchemy_crud_plus.types import CreateSchema, Model, UpdateSchema
from sqlalchemy_crud_plus.utils import apply_sorting, parse_filters


class CRUDPlus(Generic[Model]):
    def __init__(self, model: type[Model]):
        self.model = model
        self.primary_key = self._get_primary_key()

    def _get_primary_key(self) -> Column | list[Column]:
        """
        Dynamically retrieve the primary key column(s) for the model.
        """
        mapper = inspect(self.model)
        primary_key = mapper.primary_key
        if len(primary_key) == 1:
            return primary_key[0]
        else:
            return list(primary_key)

    def _get_pk_filter(self, pk: Any | Sequence[Any]) -> list[ColumnExpressionArgument[bool]]:
        """
        Get the primary key filter(s).

        :param pk: Single value for simple primary key, or tuple for composite primary key
        :return:
        """
        if isinstance(self.primary_key, list):
            if len(pk) != len(self.primary_key):
                raise CompositePrimaryKeysError(f'Expected {len(self.primary_key)} values for composite primary key')
            return [column == value for column, value in zip(self.primary_key, pk)]
        else:
            return [self.primary_key == pk]

    async def create_model(
        self,
        session: AsyncSession,
        obj: CreateSchema,
        flush: bool = False,
        commit: bool = False,
        **kwargs,
    ) -> Model:
        """
        Create a new instance of a model.

        :param session: The SQLAlchemy async session
        :param obj: The Pydantic schema containing data to be saved
        :param flush: If `True`, flush all object changes to the database
        :param commit: If `True`, commits the transaction immediately
        :param kwargs: Additional model data not included in the pydantic schema
        :return:
        """
        obj_data = obj.model_dump()
        if kwargs:
            obj_data.update(kwargs)

        ins = self.model(**obj_data)
        session.add(ins)

        if flush:
            await session.flush()
        if commit:
            await session.commit()

        return ins

    async def create_models(
        self,
        session: AsyncSession,
        objs: Iterable[CreateSchema],
        flush: bool = False,
        commit: bool = False,
        **kwargs,
    ) -> list[Model]:
        """
        Create new instances of a model.

        :param session: The SQLAlchemy async session
        :param objs: The Pydantic schema list containing data to be saved
        :param flush: If `True`, flush all object changes to the database
        :param commit: If `True`, commits the transaction immediately
        :param kwargs: Additional model data not included in the pydantic schema
        :return:
        """
        ins_list = []
        for obj in objs:
            obj_data = obj.model_dump()
            if kwargs:
                obj_data.update(kwargs)
            ins = self.model(**obj_data)
            ins_list.append(ins)

        session.add_all(ins_list)

        if flush:
            await session.flush()
        if commit:
            await session.commit()

        return ins_list

    async def count(
        self,
        session: AsyncSession,
        *whereclause: ColumnExpressionArgument[bool],
        **kwargs,
    ) -> int:
        """
        Count records that match specified filters.

        :param session: The SQLAlchemy async session
        :param whereclause: Additional WHERE clauses to apply to the query
        :param kwargs: Filter expressions using field__operator=value syntax
        :return:
        """
        filters = list(whereclause)

        if kwargs:
            filters.extend(parse_filters(self.model, **kwargs))

        stmt = select(func.count()).select_from(self.model)
        if filters:
            stmt = stmt.where(*filters)

        query = await session.execute(stmt)
        total_count = query.scalar()
        return total_count if total_count is not None else 0

    async def exists(
        self,
        session: AsyncSession,
        *whereclause: ColumnExpressionArgument[bool],
        **kwargs,
    ) -> bool:
        """
        Check whether records that match the specified filters exist.

        :param session: The SQLAlchemy async session
        :param whereclause: Additional WHERE clauses to apply to the query
        :param kwargs: Filter expressions using field__operator=value syntax
        :return:
        """
        filter_list = list(whereclause)

        if kwargs:
            filter_list.extend(parse_filters(self.model, **kwargs))

        stmt = select(self.model).where(*filter_list).limit(1)
        query = await session.execute(stmt)
        return query.scalars().first() is not None

    async def select_model(
        self,
        session: AsyncSession,
        pk: Any | Sequence[Any],
        *whereclause: ColumnExpressionArgument[bool],
        options: list[Any] | None = None,
    ) -> Model | None:
        """
        Query by primary key(s)

        :param session: The SQLAlchemy async session.
        :param pk: Single value for simple primary key, or tuple for composite primary key.
        :param whereclause: The WHERE clauses to apply to the query.
        :param options: List of SQLAlchemy options (selectinload, joinedload, etc.)
        :return:
        """
        filters = self._get_pk_filter(pk)
        filters.extend(list(whereclause))
        stmt = select(self.model).where(*filters)

        if options:
            stmt = apply_options(stmt, options)

        query = await session.execute(stmt)
        return query.scalars().first()

    async def select_model_by_column(
        self,
        session: AsyncSession,
        *whereclause: ColumnExpressionArgument[bool],
        options: list[Any] | None = None,
        joins: list[dict] | None = None,
        **kwargs,
    ) -> Model | None:
        """
        Query by column

        :param session: The SQLAlchemy async session.
        :param whereclause: The WHERE clauses to apply to the query.
        :param options: List of SQLAlchemy options (selectinload, joinedload, etc.)
        :param joins: List of join configurations for manual joins
        :param kwargs: Query expressions.
        :return:
        """
        # Use advanced query builder if joins are specified
        if joins:
            stmt = build_advanced_query(
                self.model,
                *whereclause,
                joins=joins,
                options=options,
                **kwargs
            )
        else:
            # Separate relationship filters from regular filters
            regular_filters, relationship_filters = parse_relationship_filters(self.model, **kwargs)

            # Build filters
            filters = parse_filters(self.model, **regular_filters) + list(whereclause)

            # Add relationship filters
            if relationship_filters:
                filters.extend(build_relationship_filters(self.model, relationship_filters))

            stmt = select(self.model).where(*filters)

            # Apply options if provided
            stmt = apply_options(stmt, options)

        query = await session.execute(stmt)
        return query.scalars().first()

    async def select(self, *whereclause: ColumnExpressionArgument[bool], **kwargs) -> Select:
        """
        Construct the SQLAlchemy selection

        :param whereclause: The WHERE clauses to apply to the query.
        :param kwargs: Query expressions.
        :return:
        """
        filters = parse_filters(self.model, **kwargs) + list(whereclause)
        stmt = select(self.model).where(*filters)
        return stmt

    async def select_order(
        self,
        sort_columns: str | list[str],
        sort_orders: str | list[str] | None = None,
        *whereclause: ColumnExpressionArgument[bool],
        **kwargs,
    ) -> Select:
        """
        Constructing SQLAlchemy selection with sorting

        :param sort_columns: more details see apply_sorting
        :param sort_orders: more details see apply_sorting
        :param whereclause: The WHERE clauses to apply to the query.
        :param kwargs: Query expressions.
        :return:
        """
        stmt = await self.select(*whereclause, **kwargs)
        sorted_stmt = apply_sorting(self.model, stmt, sort_columns, sort_orders)
        return sorted_stmt

    async def select_models(
        self,
        session: AsyncSession,
        *whereclause: ColumnExpressionArgument[bool],
        options: list[Any] | None = None,
        joins: list[dict] | None = None,
        **kwargs,
    ) -> Sequence[Model]:
        """
        Query all rows that match the specified filters.

        :param session: The SQLAlchemy async session
        :param whereclause: Additional WHERE clauses to apply to the query
        :param options: List of SQLAlchemy options (selectinload, joinedload, etc.)
        :param joins: List of join configurations for manual joins
        :param kwargs: Filter expressions using field__operator=value syntax
        :return:
        """
        # Use advanced query builder if joins are specified
        if joins:
            stmt = build_advanced_query(
                self.model,
                *whereclause,
                joins=joins,
                options=options,
                **kwargs
            )
        else:
            # Separate relationship filters from regular filters
            regular_filters, relationship_filters = parse_relationship_filters(self.model, **kwargs)

            # Build filters
            filters = parse_filters(self.model, **regular_filters) + list(whereclause)

            # Add relationship filters
            if relationship_filters:
                filters.extend(build_relationship_filters(self.model, relationship_filters))

            stmt = select(self.model).where(*filters)

            # Apply options if provided
            stmt = apply_options(stmt, options)

        query = await session.execute(stmt)
        return query.scalars().all()

    async def select_models_order(
        self,
        session: AsyncSession,
        sort_columns: str | list[str],
        sort_orders: str | list[str] | None = None,
        *whereclause: ColumnExpressionArgument[bool],
        options: list[Any] | None = None,
        joins: list[dict] | None = None,
        **kwargs,
    ) -> Sequence[Model]:
        """
        Query all rows that match the specified filters and sort by columns.

        :param session: The SQLAlchemy async session
        :param sort_columns: Column name(s) to sort by
        :param sort_orders: Sort order(s) ('asc' or 'desc')
        :param whereclause: Additional WHERE clauses to apply to the query
        :param options: List of SQLAlchemy options (selectinload, joinedload, etc.)
        :param joins: List of join configurations for manual joins
        :param kwargs: Filter expressions using field__operator=value syntax
        :return:
        """
        # Use advanced query builder
        stmt = build_advanced_query(
            self.model,
            *whereclause,
            joins=joins,
            options=options,
            sort_columns=sort_columns,
            sort_orders=sort_orders,
            **kwargs
        )

        query = await session.execute(stmt)
        return query.scalars().all()

    async def update_model(
        self,
        session: AsyncSession,
        pk: Any | Sequence[Any],
        obj: UpdateSchema | dict[str, Any],
        flush: bool = False,
        commit: bool = False,
        **kwargs,
    ) -> int:
        """
        Update an instance by model's primary key

        :param session: The SQLAlchemy async session.
        :param pk: Single value for simple primary key, or tuple for composite primary key.
        :param obj: A pydantic schema or dictionary containing the update data
        :param flush: If `True`, flush all object changes to the database. Default is `False`.
        :param commit: If `True`, commits the transaction immediately. Default is `False`.
        :param kwargs: Additional model data not included in the pydantic schema.
        :return:
        """
        filters = self._get_pk_filter(pk)
        instance_data = obj if isinstance(obj, dict) else obj.model_dump(exclude_unset=True)
        instance_data.update(kwargs)
        stmt = update(self.model).where(*filters).values(**instance_data)
        result = await session.execute(stmt)

        if flush:
            await session.flush()
        if commit:
            await session.commit()

        return result.rowcount  # type: ignore

    async def update_model_by_column(
        self,
        session: AsyncSession,
        obj: UpdateSchema | dict[str, Any],
        allow_multiple: bool = False,
        flush: bool = False,
        commit: bool = False,
        **kwargs,
    ) -> int:
        """
        Update records by model column filters.

        :param session: The SQLAlchemy async session
        :param obj: A Pydantic schema or dictionary containing the update data
        :param allow_multiple: If `True`, allows updating multiple records that match the filters
        :param flush: If `True`, flush all object changes to the database
        :param commit: If `True`, commits the transaction immediately
        :param kwargs: Filter expressions using field__operator=value syntax
        :return:
        """
        filters = parse_filters(self.model, **kwargs)

        if not filters:
            raise ValueError('At least one filter condition must be provided for update operation')

        if not allow_multiple:
            total_count = await self.count(session, *filters)
            if total_count > 1:
                raise MultipleResultsError(f'Only one record is expected to be updated, found {total_count} records.')

        instance_data = obj if isinstance(obj, dict) else obj.model_dump(exclude_unset=True)
        stmt = update(self.model).where(*filters).values(**instance_data)
        result = await session.execute(stmt)

        if flush:
            await session.flush()
        if commit:
            await session.commit()

        return result.rowcount  # type: ignore

    async def delete_model(
        self,
        session: AsyncSession,
        pk: Any | Sequence[Any],
        flush: bool = False,
        commit: bool = False,
    ) -> int:
        """
        Delete an instance by model's primary key

        :param session: The SQLAlchemy async session.
        :param pk: Single value for simple primary key, or tuple for composite primary key.
        :param flush: If `True`, flush all object changes to the database. Default is `False`.
        :param commit: If `True`, commits the transaction immediately. Default is `False`.
        :return:
        """
        filters = self._get_pk_filter(pk)

        stmt = delete(self.model).where(*filters)
        result = await session.execute(stmt)

        if flush:
            await session.flush()
        if commit:
            await session.commit()

        return result.rowcount  # type: ignore

    async def delete_model_by_column(
        self,
        session: AsyncSession,
        allow_multiple: bool = False,
        logical_deletion: bool = False,
        deleted_flag_column: str = 'del_flag',
        flush: bool = False,
        commit: bool = False,
        **kwargs,
    ) -> int:
        """
        Delete records by model column filters.

        :param session: The SQLAlchemy async session
        :param allow_multiple: If `True`, allows deleting multiple records that match the filters
        :param logical_deletion: If `True`, enable logical deletion instead of physical deletion
        :param deleted_flag_column: Column name for logical deletion flag
        :param flush: If `True`, flush all object changes to the database
        :param commit: If `True`, commits the transaction immediately
        :param kwargs: Filter expressions using field__operator=value syntax
        :return:
        """
        if logical_deletion:
            if not hasattr(self.model, deleted_flag_column):
                raise ModelColumnError(f'Column {deleted_flag_column} is not found in {self.model}')

        filters = parse_filters(self.model, **kwargs)

        if not filters:
            raise ValueError('At least one filter condition must be provided for delete operation')

        if not allow_multiple:
            total_count = await self.count(session, *filters)
            if total_count > 1:
                raise MultipleResultsError(f'Only one record is expected to be deleted, found {total_count} records.')

        stmt = (
            update(self.model).where(*filters).values(**{deleted_flag_column: True})
            if logical_deletion
            else delete(self.model).where(*filters)
        )

        result = await session.execute(stmt)

        if flush:
            await session.flush()
        if commit:
            await session.commit()

        return result.rowcount  # type: ignore

    # ==================== Advanced Query Methods ====================

    async def query(
        self,
        session: AsyncSession,
        *whereclause: ColumnExpressionArgument[bool],
        joins: list[dict] | None = None,
        options: list[Any] | None = None,
        sort_columns: str | list[str] | None = None,
        sort_orders: str | list[str] | None = None,
        limit: int | None = None,
        offset: int | None = None,
        **kwargs,
    ) -> Sequence[Model]:
        """
        Advanced query method with comprehensive relationship support.

        :param session: The SQLAlchemy async session
        :param whereclause: Additional WHERE clauses to apply to the query
        :param joins: List of join configurations for manual joins
        :param options: List of SQLAlchemy options (loading strategies, etc.)
        :param sort_columns: Column name(s) to sort by
        :param sort_orders: Sort order(s) ('asc' or 'desc')
        :param limit: Maximum number of results to return
        :param offset: Number of results to skip
        :param kwargs: Filter expressions using field__operator=value syntax
        :return: Sequence of model instances
        """
        # Build advanced query
        stmt = build_advanced_query(
            self.model,
            *whereclause,
            joins=joins,
            options=options,
            sort_columns=sort_columns,
            sort_orders=sort_orders,
            **kwargs
        )

        # Apply limit and offset
        if limit is not None:
            stmt = stmt.limit(limit)
        if offset is not None:
            stmt = stmt.offset(offset)

        query = await session.execute(stmt)
        return query.scalars().all()

    async def query_one(
        self,
        session: AsyncSession,
        *whereclause: ColumnExpressionArgument[bool],
        joins: list[dict] | None = None,
        options: list[Any] | None = None,
        **kwargs,
    ) -> Model | None:
        """
        Advanced query method to get single result with comprehensive relationship support.

        :param session: The SQLAlchemy async session
        :param whereclause: Additional WHERE clauses to apply to the query
        :param joins: List of join configurations for manual joins
        :param options: List of SQLAlchemy options (loading strategies, etc.)
        :param kwargs: Filter expressions using field__operator=value syntax
        :return: Model instance or None
        """
        # Build advanced query
        stmt = build_advanced_query(
            self.model,
            *whereclause,
            joins=joins,
            options=options,
            **kwargs
        )

        query = await session.execute(stmt)
        return query.scalars().first()

    async def query_with_joins(
        self,
        session: AsyncSession,
        *whereclause: ColumnExpressionArgument[bool],
        inner_joins: list[str] | None = None,
        left_joins: list[str] | None = None,
        right_joins: list[str] | None = None,
        options: list[Any] | None = None,
        sort_columns: str | list[str] | None = None,
        sort_orders: str | list[str] | None = None,
        **kwargs,
    ) -> Sequence[Model]:
        """
        Query with convenient join methods.

        :param session: The SQLAlchemy async session
        :param whereclause: Additional WHERE clauses to apply to the query
        :param inner_joins: List of relationship names for inner joins
        :param left_joins: List of relationship names for left outer joins
        :param right_joins: List of relationship names for right outer joins
        :param options: List of SQLAlchemy options (loading strategies, etc.)
        :param sort_columns: Column name(s) to sort by
        :param sort_orders: Sort order(s) ('asc' or 'desc')
        :param kwargs: Filter expressions using field__operator=value syntax
        :return: Sequence of model instances
        """
        joins = []

        # Build join configurations
        if inner_joins:
            for rel_name in inner_joins:
                joins.append({'target': rel_name, 'type': 'inner'})

        if left_joins:
            for rel_name in left_joins:
                joins.append({'target': rel_name, 'type': 'left'})

        if right_joins:
            for rel_name in right_joins:
                joins.append({'target': rel_name, 'type': 'right'})

        return await self.query(
            session,
            *whereclause,
            joins=joins,
            options=options,
            sort_columns=sort_columns,
            sort_orders=sort_orders,
            **kwargs
        )

    async def query_with_relationships(
        self,
        session: AsyncSession,
        relationships: str | list[str] | dict[str, Any] | None = None,
        strategy: str = 'selectinload',
        *whereclause: ColumnExpressionArgument[bool],
        sort_columns: str | list[str] | None = None,
        sort_orders: str | list[str] | None = None,
        limit: int | None = None,
        offset: int | None = None,
        **kwargs,
    ) -> Sequence[Model]:
        """
        Query with automatic relationship loading options.

        :param session: The SQLAlchemy async session
        :param relationships: Relationships to load (str, list, or dict for nested)
        :param strategy: Loading strategy ('selectinload', 'joinedload', etc.)
        :param whereclause: Additional WHERE clauses to apply to the query
        :param sort_columns: Column name(s) to sort by
        :param sort_orders: Sort order(s) ('asc' or 'desc')
        :param limit: Maximum number of results to return
        :param offset: Number of results to skip
        :param kwargs: Filter expressions using field__operator=value syntax
        :return: Sequence of model instances
        """
        # Create loading options
        options = create_loading_options(self.model, relationships, strategy)

        return await self.query(
            session,
            *whereclause,
            options=options,
            sort_columns=sort_columns,
            sort_orders=sort_orders,
            limit=limit,
            offset=offset,
            **kwargs
        )
