#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Data integrity and edge case tests for SQLAlchemy CRUD Plus.
"""
import pytest
from datetime import datetime, timedelta
from decimal import Decimal

from sqlalchemy import Column, Integer, String, DateTime, Boolean, Numeric, Text
from sqlalchemy.orm import DeclarativeBase

from sqlalchemy_crud_plus import CRUDPlus
from tests.model import Ins, InsPks
from tests.schema import ModelTest, ModelTestPks


class TestBase(DeclarativeBase):
    """Base class for test models."""
    pass


class ExtendedTestModel(TestBase):
    """Extended test model with various data types."""
    __tablename__ = 'extended_test_model'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)
    description = Column(Text)
    price = Column(Numeric(10, 2))
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    del_flag = Column(<PERSON>olean, default=False)


class TestDataIntegrity:
    """Test data integrity and edge cases."""

    @pytest.mark.asyncio
    async def test_unicode_and_special_characters(self, async_db_session):
        """Test handling of Unicode and special characters."""
        async with async_db_session.begin() as session:
            crud = CRUDPlus(Ins)
            
            # Test various Unicode characters
            unicode_test_cases = [
                '测试中文字符',  # Chinese
                'тест кириллица',  # Cyrillic
                'テスト日本語',  # Japanese
                '🚀🌟💫 Emoji test',  # Emojis
                'Ñoño español ñ',  # Spanish with tildes
                'café français ç',  # French with cedilla
                'Ελληνικά αβγ',  # Greek
                'العربية',  # Arabic
                'עברית',  # Hebrew
            ]
            
            created_records = []
            for i, name in enumerate(unicode_test_cases):
                data = ModelTest(name=name)
                record = await crud.create_model(session, data)
                created_records.append(record)
                
                # Verify immediate retrieval
                retrieved = await crud.select_model(session, record.id)
                assert retrieved.name == name, f"Unicode mismatch for: {name}"
            
            # Test querying with Unicode
            for record in created_records:
                found = await crud.select_model_by_column(session, name=record.name)
                assert found is not None
                assert found.name == record.name

    @pytest.mark.asyncio
    async def test_special_sql_characters(self, async_db_session):
        """Test handling of characters that have special meaning in SQL."""
        async with async_db_session.begin() as session:
            crud = CRUDPlus(Ins)
            
            special_chars_test_cases = [
                "test'single'quote",
                'test"double"quote',
                'test\\backslash',
                'test%percent',
                'test_underscore',
                'test[square]brackets',
                'test(round)parentheses',
                'test{curly}braces',
                'test;semicolon',
                'test--comment',
                'test/*comment*/',
                'test\nNewline',
                'test\tTab',
                'test\r\nCRLF',
            ]
            
            for test_name in special_chars_test_cases:
                data = ModelTest(name=test_name)
                record = await crud.create_model(session, data)
                
                # Test exact match retrieval
                found = await crud.select_model_by_column(session, name=test_name)
                assert found is not None, f"Failed to find record with name: {test_name}"
                assert found.name == test_name
                
                # Test LIKE operations with special characters
                if '%' not in test_name and '_' not in test_name:
                    like_results = await crud.select_models(session, name__like=f"{test_name[:5]}%")
                    assert len(like_results) >= 1

    @pytest.mark.asyncio
    async def test_null_and_empty_value_handling(self, async_db_session):
        """Test handling of NULL and empty values."""
        async with async_db_session.begin() as session:
            crud = CRUDPlus(Ins)
            
            # Test empty string
            empty_data = ModelTest(name='')
            empty_record = await crud.create_model(session, empty_data)
            assert empty_record.name == ''
            
            # Test retrieval of empty string
            found_empty = await crud.select_model_by_column(session, name='')
            assert found_empty is not None
            assert found_empty.name == ''
            
            # Test whitespace-only string
            whitespace_data = ModelTest(name='   ')
            whitespace_record = await crud.create_model(session, whitespace_data)
            assert whitespace_record.name == '   '
            
            # Test NULL-like operations
            null_results = await crud.select_models(session, name__is=None)
            assert isinstance(null_results, list)  # Should not crash
            
            not_null_results = await crud.select_models(session, name__is_not=None)
            assert len(not_null_results) >= 2  # At least our test records

    @pytest.mark.asyncio
    async def test_large_text_data(self, async_db_session):
        """Test handling of large text data."""
        async with async_db_session.begin() as session:
            crud = CRUDPlus(Ins)
            
            # Test near-limit text (String(64) in model)
            large_text = 'A' * 60  # Close to limit
            data = ModelTest(name=large_text)
            record = await crud.create_model(session, data)
            
            retrieved = await crud.select_model(session, record.id)
            assert retrieved.name == large_text
            assert len(retrieved.name) == 60
            
            # Test exact limit
            limit_text = 'B' * 64  # Exact limit
            limit_data = ModelTest(name=limit_text)
            limit_record = await crud.create_model(session, limit_data)
            
            retrieved_limit = await crud.select_model(session, limit_record.id)
            assert retrieved_limit.name == limit_text
            assert len(retrieved_limit.name) == 64

    @pytest.mark.asyncio
    async def test_datetime_edge_cases(self, async_db_session):
        """Test datetime handling edge cases."""
        async with async_db_session.begin() as session:
            crud = CRUDPlus(Ins)
            
            # Create a record and test various datetime queries
            data = ModelTest(name='datetime_test')
            record = await crud.create_model(session, data)
            
            # Test datetime range queries
            now = datetime.now()
            yesterday = now - timedelta(days=1)
            tomorrow = now + timedelta(days=1)
            
            # Query records created today
            today_records = await crud.select_models(
                session,
                created_time__between=[yesterday, tomorrow]
            )
            assert len(today_records) >= 1
            
            # Query records created before yesterday (should be empty)
            old_records = await crud.select_models(
                session,
                created_time__lt=yesterday
            )
            # Should be empty for our test record
            test_record_in_old = any(r.id == record.id for r in old_records)
            assert not test_record_in_old
            
            # Query records created after tomorrow (should be empty)
            future_records = await crud.select_models(
                session,
                created_time__gt=tomorrow
            )
            test_record_in_future = any(r.id == record.id for r in future_records)
            assert not test_record_in_future

    @pytest.mark.asyncio
    async def test_boolean_field_operations(self, async_db_session):
        """Test boolean field operations and edge cases."""
        async with async_db_session.begin() as session:
            crud = CRUDPlus(Ins)
            
            # Create records with different boolean values
            true_data = ModelTest(name='true_record')
            true_record = await crud.create_model(session, true_data, del_flag=True)
            
            false_data = ModelTest(name='false_record')
            false_record = await crud.create_model(session, false_data, del_flag=False)
            
            # Test boolean equality queries
            true_records = await crud.select_models(session, del_flag=True)
            false_records = await crud.select_models(session, del_flag=False)
            
            assert any(r.id == true_record.id for r in true_records)
            assert any(r.id == false_record.id for r in false_records)
            
            # Test boolean IS operations
            is_true_records = await crud.select_models(session, del_flag__is=True)
            is_false_records = await crud.select_models(session, del_flag__is=False)
            
            assert any(r.id == true_record.id for r in is_true_records)
            assert any(r.id == false_record.id for r in is_false_records)
            
            # Test NOT operations
            not_true_records = await crud.select_models(session, del_flag__ne=True)
            assert any(r.id == false_record.id for r in not_true_records)

    @pytest.mark.asyncio
    async def test_composite_primary_key_edge_cases(self, create_test_model_pks, async_db_session):
        """Test composite primary key edge cases."""
        async with async_db_session() as session:
            crud = CRUDPlus(InsPks)
            
            # Test querying existing composite key
            result = await crud.select_model(session, (1, 'men'))
            assert result is not None
            assert result.id == 1
            assert result.sex == 'men'
            
            # Test querying non-existent composite key
            result = await crud.select_model(session, (999, 'unknown'))
            assert result is None
            
            # Test updating with composite key
            update_data = ModelTestPks(name='updated_name')
            updated_count = await crud.update_model(session, (1, 'men'), update_data)
            assert updated_count == 1
            
            # Verify update
            updated_record = await crud.select_model(session, (1, 'men'))
            assert updated_record.name == 'updated_name'

    @pytest.mark.asyncio
    async def test_transaction_isolation_and_consistency(self, async_db_session):
        """Test transaction isolation and data consistency."""
        crud = CRUDPlus(Ins)
        
        # Test transaction isolation
        async with async_db_session.begin() as session1:
            data = ModelTest(name='isolation_test')
            record = await crud.create_model(session1, data)
            record_id = record.id
            
            # In another session, the record should not be visible yet
            async with async_db_session() as session2:
                result = await crud.select_model(session2, record_id)
                # Depending on isolation level, this might be None
                # We just ensure no exception is raised
        
        # After commit, record should be visible
        async with async_db_session() as session:
            result = await crud.select_model(session, record_id)
            assert result is not None
            assert result.name == 'isolation_test'

    @pytest.mark.asyncio
    async def test_concurrent_modifications(self, async_db_session):
        """Test concurrent modifications and race conditions."""
        import asyncio
        
        # Create initial record
        async with async_db_session.begin() as session:
            crud = CRUDPlus(Ins)
            data = ModelTest(name='concurrent_test')
            record = await crud.create_model(session, data)
            record_id = record.id
        
        async def update_record(suffix: str):
            """Update the record with a suffix."""
            async with async_db_session.begin() as session:
                crud = CRUDPlus(Ins)
                update_data = ModelTest(name=f'updated_{suffix}')
                return await crud.update_model(session, record_id, update_data)
        
        # Run concurrent updates
        tasks = [update_record(f'task_{i}') for i in range(3)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # At least one update should succeed
        successful_updates = [r for r in results if isinstance(r, int) and r > 0]
        assert len(successful_updates) >= 1
        
        # Verify final state
        async with async_db_session() as session:
            crud = CRUDPlus(Ins)
            final_record = await crud.select_model(session, record_id)
            assert final_record is not None
            assert 'updated_' in final_record.name

    @pytest.mark.asyncio
    async def test_rollback_data_consistency(self, async_db_session):
        """Test data consistency after transaction rollback."""
        crud = CRUDPlus(Ins)
        
        # Get initial count
        async with async_db_session() as session:
            initial_count = await crud.count(session)
        
        # Perform operations in a transaction that will be rolled back
        try:
            async with async_db_session.begin() as session:
                # Create multiple records
                data_list = [ModelTest(name=f'rollback_test_{i}') for i in range(5)]
                await crud.create_models(session, data_list)
                
                # Verify records exist in transaction
                count_in_tx = await crud.count(session, name__startswith='rollback_test_')
                assert count_in_tx == 5
                
                # Force rollback
                raise Exception("Intentional rollback")
        except Exception:
            pass
        
        # Verify rollback worked - no new records should exist
        async with async_db_session() as session:
            final_count = await crud.count(session)
            rollback_count = await crud.count(session, name__startswith='rollback_test_')
            
            assert final_count == initial_count
            assert rollback_count == 0

    @pytest.mark.asyncio
    async def test_logical_deletion_integrity(self, async_db_session):
        """Test logical deletion data integrity."""
        async with async_db_session.begin() as session:
            crud = CRUDPlus(Ins)
            
            # Create test record
            data = ModelTest(name='logical_delete_test')
            record = await crud.create_model(session, data)
            original_id = record.id
            
            # Perform logical deletion
            deleted_count = await crud.delete_model_by_column(
                session,
                logical_deletion=True,
                name='logical_delete_test'
            )
            assert deleted_count == 1
            
            # Verify record still exists but is marked as deleted
            deleted_record = await crud.select_model(session, original_id)
            assert deleted_record is not None
            assert deleted_record.del_flag is True
            assert deleted_record.name == 'logical_delete_test'
            
            # Verify filtering by deletion flag
            active_records = await crud.select_models(session, del_flag=False)
            deleted_records = await crud.select_models(session, del_flag=True)
            
            assert any(r.id == original_id for r in deleted_records)
            assert not any(r.id == original_id for r in active_records)

    @pytest.mark.asyncio
    async def test_update_with_same_values(self, async_db_session):
        """Test updating records with the same values."""
        async with async_db_session.begin() as session:
            crud = CRUDPlus(Ins)
            
            # Create record
            original_name = 'same_value_test'
            data = ModelTest(name=original_name)
            record = await crud.create_model(session, data)
            
            # Update with same value
            update_data = ModelTest(name=original_name)
            result = await crud.update_model(session, record.id, update_data)
            assert result == 1
            
            # Verify data unchanged
            updated_record = await crud.select_model(session, record.id)
            assert updated_record.name == original_name
            
            # Test partial update with same values
            partial_update = {'name': original_name}
            result = await crud.update_model(session, record.id, partial_update)
            assert result == 1

    @pytest.mark.asyncio
    async def test_delete_and_recreate_cycle(self, async_db_session):
        """Test delete and recreate cycle for data integrity."""
        crud = CRUDPlus(Ins)
        original_name = 'delete_recreate_test'

        # Create record
        async with async_db_session.begin() as session:
            data = ModelTest(name=original_name)
            record = await crud.create_model(session, data)
            original_id = record.id

        # Delete record
        async with async_db_session.begin() as session:
            result = await crud.delete_model(session, original_id)
            assert result == 1

        # Verify deletion
        async with async_db_session() as session:
            result = await crud.select_model(session, original_id)
            assert result is None

        # Recreate with same name
        async with async_db_session.begin() as session:
            new_data = ModelTest(name=original_name)
            new_record = await crud.create_model(session, new_data)
            new_id = new_record.id

            # Should have different ID
            assert new_id != original_id
            assert new_record.name == original_name

        # Verify new record exists and old one doesn't
        async with async_db_session() as session:
            old_record = await crud.select_model(session, original_id)
            new_record_retrieved = await crud.select_model(session, new_id)

            assert old_record is None
            assert new_record_retrieved is not None
            assert new_record_retrieved.name == original_name
