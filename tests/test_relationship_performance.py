#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import pytest
import pytest_asyncio
import time
from typing import AsyncGenerator

from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine
from sqlalchemy.orm import selectinload, joinedload, subqueryload

from sqlalchemy_crud_plus import CRUDPlus
from tests.relationship_models import (
    RelationshipBase, User, UserProfile, Post, Comment, Category, Role, Tag,
    user_role_table, post_tag_table
)


# Database configuration for performance tests
_async_engine = create_async_engine(
    'sqlite+aiosqlite:///:memory:',
    future=True,
    echo=False,
)
_async_session_factory = async_sessionmaker(_async_engine, autoflush=False, expire_on_commit=False)


@pytest_asyncio.fixture(scope='function', autouse=True)
async def setup_performance_database() -> AsyncGenerator[None, None]:
    """Setup and teardown database for each test function."""
    async with _async_engine.begin() as conn:
        await conn.run_sync(RelationshipBase.metadata.create_all)
    yield
    async with _async_engine.begin() as conn:
        await conn.run_sync(RelationshipBase.metadata.drop_all)


@pytest_asyncio.fixture
async def db_session() -> AsyncGenerator[AsyncSession, None]:
    """Provide a database session for testing."""
    async with _async_session_factory() as session:
        yield session


@pytest_asyncio.fixture
async def large_dataset(db_session: AsyncSession) -> dict:
    """Create a large dataset for performance testing."""
    async with db_session.begin():
        # Create 100 users
        users = [
            User(username=f'user_{i}', email=f'user{i}@example.com', full_name=f'User {i}')
            for i in range(1, 101)
        ]
        db_session.add_all(users)
        await db_session.flush()

        # Create profiles for half of the users
        profiles = [
            UserProfile(user_id=users[i].id, bio=f'Bio for user {i+1}', location=f'City {i+1}')
            for i in range(0, 50)
        ]
        db_session.add_all(profiles)

        # Create 20 categories
        categories = [
            Category(name=f'Category {i}', description=f'Description for category {i}')
            for i in range(1, 21)
        ]
        db_session.add_all(categories)
        await db_session.flush()

        # Create 500 posts
        posts = [
            Post(
                title=f'Post {i}',
                content=f'Content of post {i}' * 10,  # Longer content
                author_id=users[i % len(users)].id,
                category_id=categories[i % len(categories)].id,
                is_published=i % 3 == 0,
                view_count=i * 5
            )
            for i in range(1, 501)
        ]
        db_session.add_all(posts)
        await db_session.flush()

        # Create 1000 comments
        comments = [
            Comment(
                content=f'Comment {i}',
                author_id=users[i % len(users)].id,
                post_id=posts[i % len(posts)].id,
                is_approved=i % 4 != 0
            )
            for i in range(1, 1001)
        ]
        db_session.add_all(comments)
        await db_session.flush()

        # Create 10 roles
        roles = [
            Role(name=f'role_{i}', description=f'Role {i} description')
            for i in range(1, 11)
        ]
        db_session.add_all(roles)
        await db_session.flush()

        # Create 20 tags
        tags = [
            Tag(name=f'tag_{i}', color=f'#00{i:04x}'[:7])
            for i in range(1, 21)
        ]
        db_session.add_all(tags)
        await db_session.flush()

        # Create many-to-many relationships
        user_role_data = []
        for i, user in enumerate(users):
            # Each user gets 1-3 roles
            role_count = (i % 3) + 1
            for j in range(role_count):
                role_idx = (i + j) % len(roles)
                user_role_data.append({'user_id': user.id, 'role_id': roles[role_idx].id})

        await db_session.execute(user_role_table.insert().values(user_role_data))

        post_tag_data = []
        for i, post in enumerate(posts):
            # Each post gets 1-4 tags
            tag_count = (i % 4) + 1
            for j in range(tag_count):
                tag_idx = (i + j) % len(tags)
                post_tag_data.append({'post_id': post.id, 'tag_id': tags[tag_idx].id})

        await db_session.execute(post_tag_table.insert().values(post_tag_data))

        await db_session.commit()

    return {
        'users': users,
        'profiles': profiles,
        'categories': categories,
        'posts': posts,
        'comments': comments,
        'roles': roles,
        'tags': tags,
    }


@pytest.fixture
def user_crud() -> CRUDPlus[User]:
    return CRUDPlus(User)


@pytest.fixture
def post_crud() -> CRUDPlus[Post]:
    return CRUDPlus(Post)


@pytest.fixture
def comment_crud() -> CRUDPlus[Comment]:
    return CRUDPlus(Comment)


class TestLoadingStrategyPerformance:
    """Test performance of different loading strategies."""

    async def test_selectinload_performance(self, db_session: AsyncSession, user_crud: CRUDPlus[User], large_dataset):
        """Test selectinload performance."""
        start_time = time.time()
        
        users = await user_crud.select_models(
            db_session,
            options=[selectinload(User.posts), selectinload(User.roles)],
            limit=50
        )
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        assert len(users) == 50
        assert execution_time < 5.0  # Should complete within 5 seconds
        
        # Verify relationships are loaded
        for user in users:
            assert hasattr(user, 'posts')
            assert hasattr(user, 'roles')

    async def test_joinedload_performance(self, db_session: AsyncSession, user_crud: CRUDPlus[User], large_dataset):
        """Test joinedload performance."""
        start_time = time.time()
        
        users = await user_crud.select_models(
            db_session,
            options=[joinedload(User.profile)],
            limit=50
        )
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        assert len(users) == 50
        assert execution_time < 3.0  # Should be faster for one-to-one relationships
        
        # Verify profile relationship is loaded
        for user in users:
            assert hasattr(user, 'profile')

    async def test_subqueryload_performance(self, db_session: AsyncSession, user_crud: CRUDPlus[User], large_dataset):
        """Test subqueryload performance."""
        start_time = time.time()
        
        users = await user_crud.select_models(
            db_session,
            options=[subqueryload(User.posts)],
            limit=30
        )
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        assert len(users) == 30
        assert execution_time < 4.0  # Should complete within reasonable time
        
        # Verify posts are loaded
        for user in users:
            assert hasattr(user, 'posts')


class TestJoinPerformance:
    """Test performance of join operations."""

    async def test_inner_join_performance(self, db_session: AsyncSession, post_crud: CRUDPlus[Post], large_dataset):
        """Test inner join performance."""
        start_time = time.time()
        
        posts = await post_crud.query_with_joins(
            db_session,
            inner_joins=['author', 'category'],
            options=[selectinload(Post.author), selectinload(Post.category)],
            limit=100
        )
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        assert len(posts) <= 100
        assert execution_time < 3.0  # Should complete within 3 seconds
        
        # Verify joins worked
        for post in posts:
            assert post.author is not None
            assert post.category is not None

    async def test_complex_join_performance(self, db_session: AsyncSession, post_crud: CRUDPlus[Post], large_dataset):
        """Test complex join performance."""
        start_time = time.time()
        
        posts = await post_crud.query(
            db_session,
            joins=[
                {'target': 'author', 'type': 'inner'},
                {'target': 'category', 'type': 'left'},
                {'target': 'comments', 'type': 'left'}
            ],
            options=[
                selectinload(Post.author),
                selectinload(Post.category),
                selectinload(Post.comments),
                selectinload(Post.tags)
            ],
            limit=50
        )
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        assert len(posts) <= 50
        assert execution_time < 5.0  # Should complete within 5 seconds


class TestAggregationPerformance:
    """Test performance of aggregation queries."""

    async def test_count_performance(self, db_session: AsyncSession, post_crud: CRUDPlus[Post], large_dataset):
        """Test count query performance."""
        start_time = time.time()
        
        count = await post_crud.count_with_joins(
            db_session,
            joins=[{'target': 'author', 'type': 'inner'}],
            is_published=True
        )
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        assert isinstance(count, int)
        assert count >= 0
        assert execution_time < 2.0  # Count should be fast

    async def test_aggregation_performance(self, db_session: AsyncSession, post_crud: CRUDPlus[Post], large_dataset):
        """Test aggregation query performance."""
        start_time = time.time()
        
        results = await post_crud.aggregate(
            db_session,
            aggregations={
                'total_posts': 'count',
                'avg_views': ('avg', 'view_count'),
                'max_views': ('max', 'view_count'),
                'total_views': ('sum', 'view_count')
            },
            group_by=['author_id'],
            joins=[{'target': 'author', 'type': 'inner'}]
        )
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        assert len(results) > 0
        assert execution_time < 3.0  # Aggregation should complete within 3 seconds
        
        # Verify aggregation results
        for result in results:
            assert 'total_posts' in result
            assert 'avg_views' in result
            assert 'max_views' in result
            assert 'total_views' in result


class TestPaginationPerformance:
    """Test performance of pagination with relationships."""

    async def test_pagination_with_relationships(self, db_session: AsyncSession, post_crud: CRUDPlus[Post], large_dataset):
        """Test pagination performance with relationship loading."""
        start_time = time.time()
        
        # Test multiple pages
        all_posts = []
        page_size = 20
        for page in range(5):  # Test 5 pages
            posts = await post_crud.query(
                db_session,
                options=[selectinload(Post.author), selectinload(Post.tags)],
                sort_columns=['created_at'],
                sort_orders=['desc'],
                limit=page_size,
                offset=page * page_size
            )
            all_posts.extend(posts)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        assert len(all_posts) <= 100  # 5 pages * 20 per page
        assert execution_time < 8.0  # Should complete within 8 seconds
        
        # Verify pagination worked correctly
        if len(all_posts) > 1:
            for i in range(len(all_posts) - 1):
                assert all_posts[i].created_at >= all_posts[i + 1].created_at


class TestMemoryUsage:
    """Test memory efficiency of relationship queries."""

    async def test_large_result_set_memory(self, db_session: AsyncSession, comment_crud: CRUDPlus[Comment], large_dataset):
        """Test memory usage with large result sets."""
        # This test ensures we don't load too much data into memory at once
        start_time = time.time()
        
        comments = await comment_crud.query(
            db_session,
            options=[selectinload(Comment.author), selectinload(Comment.post)],
            limit=200  # Reasonable limit to avoid memory issues
        )
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        assert len(comments) <= 200
        assert execution_time < 4.0  # Should complete within 4 seconds
        
        # Verify relationships are loaded efficiently
        for comment in comments:
            assert hasattr(comment, 'author')
            assert hasattr(comment, 'post')
