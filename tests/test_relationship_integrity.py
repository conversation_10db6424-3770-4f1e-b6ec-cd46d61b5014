#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import pytest
import pytest_asyncio
from typing import AsyncGenerator

from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine
from sqlalchemy.orm import selectinload, joinedload
from sqlalchemy.exc import IntegrityError

from sqlalchemy_crud_plus import CRUDPlus
from tests.relationship_models import (
    RelationshipBase, User, UserProfile, Post, Comment, Category, Role, Tag,
    Department, Employee, user_role_table, post_tag_table
)
from tests.relationship_schemas import (
    UserCreate, UserProfileCreate, PostCreate, CommentCreate, CategoryCreate,
    RoleCreate, TagCreate, DepartmentCreate, EmployeeCreate
)


# Database configuration for integrity tests
_async_engine = create_async_engine(
    'sqlite+aiosqlite:///:memory:',
    future=True,
    echo=False,
)
_async_session_factory = async_sessionmaker(_async_engine, autoflush=False, expire_on_commit=False)


@pytest_asyncio.fixture(scope='function', autouse=True)
async def setup_integrity_database() -> AsyncGenerator[None, None]:
    """Setup and teardown database for each test function."""
    async with _async_engine.begin() as conn:
        await conn.run_sync(RelationshipBase.metadata.create_all)
    yield
    async with _async_engine.begin() as conn:
        await conn.run_sync(RelationshipBase.metadata.drop_all)


@pytest_asyncio.fixture
async def db_session() -> AsyncGenerator[AsyncSession, None]:
    """Provide a database session for testing."""
    async with _async_session_factory() as session:
        yield session


@pytest_asyncio.fixture
async def basic_data(db_session: AsyncSession) -> dict:
    """Create basic test data for integrity tests."""
    async with db_session.begin():
        # Create users
        users = [
            User(username='alice', email='<EMAIL>', full_name='Alice Smith'),
            User(username='bob', email='<EMAIL>', full_name='Bob Johnson'),
            User(username='charlie', email='<EMAIL>', full_name='Charlie Brown'),
        ]
        db_session.add_all(users)
        await db_session.flush()

        # Create categories
        categories = [
            Category(name='Technology', description='Tech posts'),
            Category(name='Science', description='Science posts'),
        ]
        db_session.add_all(categories)
        await db_session.flush()

        # Create posts
        posts = [
            Post(title='First Post', content='Content 1', author_id=users[0].id, category_id=categories[0].id),
            Post(title='Second Post', content='Content 2', author_id=users[1].id, category_id=categories[1].id),
        ]
        db_session.add_all(posts)
        await db_session.flush()

        await db_session.commit()

    return {
        'users': users,
        'categories': categories,
        'posts': posts,
    }


@pytest.fixture
def user_crud() -> CRUDPlus[User]:
    return CRUDPlus(User)


@pytest.fixture
def post_crud() -> CRUDPlus[Post]:
    return CRUDPlus(Post)


@pytest.fixture
def comment_crud() -> CRUDPlus[Comment]:
    return CRUDPlus(Comment)


@pytest.fixture
def category_crud() -> CRUDPlus[Category]:
    return CRUDPlus(Category)


@pytest.fixture
def department_crud() -> CRUDPlus[Department]:
    return CRUDPlus(Department)


@pytest.fixture
def employee_crud() -> CRUDPlus[Employee]:
    return CRUDPlus(Employee)


class TestForeignKeyIntegrity:
    """Test foreign key constraint integrity."""

    async def test_valid_foreign_key_creation(self, db_session: AsyncSession, post_crud: CRUDPlus[Post], basic_data):
        """Test creating records with valid foreign keys."""
        post_data = PostCreate(
            title='New Post',
            content='New content',
            author_id=basic_data['users'][0].id,
            category_id=basic_data['categories'][0].id
        )
        
        post = await post_crud.create_model(db_session, post_data, commit=True)
        
        assert post.id is not None
        assert post.author_id == basic_data['users'][0].id
        assert post.category_id == basic_data['categories'][0].id

    async def test_invalid_foreign_key_creation(self, db_session: AsyncSession, post_crud: CRUDPlus[Post], basic_data):
        """Test creating records with invalid foreign keys."""
        post_data = PostCreate(
            title='Invalid Post',
            content='Invalid content',
            author_id=999,  # Non-existent user
            category_id=basic_data['categories'][0].id
        )
        
        with pytest.raises(IntegrityError):
            await post_crud.create_model(db_session, post_data, commit=True)

    async def test_cascade_delete_behavior(self, db_session: AsyncSession, user_crud: CRUDPlus[User], post_crud: CRUDPlus[Post], basic_data):
        """Test cascade delete behavior."""
        user_id = basic_data['users'][0].id
        
        # Verify user has posts
        posts_before = await post_crud.select_models(db_session, author_id=user_id)
        assert len(posts_before) > 0
        
        # Delete user (should cascade to posts)
        await user_crud.delete_model(db_session, user_id, commit=True)
        
        # Verify posts are deleted
        posts_after = await post_crud.select_models(db_session, author_id=user_id)
        assert len(posts_after) == 0


class TestUniqueConstraintIntegrity:
    """Test unique constraint integrity."""

    async def test_unique_username_constraint(self, db_session: AsyncSession, user_crud: CRUDPlus[User], basic_data):
        """Test unique username constraint."""
        # Try to create user with existing username
        duplicate_user_data = UserCreate(
            username='alice',  # Already exists
            email='<EMAIL>'
        )
        
        with pytest.raises(IntegrityError):
            await user_crud.create_model(db_session, duplicate_user_data, commit=True)

    async def test_unique_email_constraint(self, db_session: AsyncSession, user_crud: CRUDPlus[User], basic_data):
        """Test unique email constraint."""
        # Try to create user with existing email
        duplicate_user_data = UserCreate(
            username='alice2',
            email='<EMAIL>'  # Already exists
        )
        
        with pytest.raises(IntegrityError):
            await user_crud.create_model(db_session, duplicate_user_data, commit=True)


class TestOneToOneIntegrity:
    """Test one-to-one relationship integrity."""

    async def test_one_to_one_creation(self, db_session: AsyncSession, basic_data):
        """Test creating one-to-one relationships."""
        user_profile_crud = CRUDPlus(UserProfile)
        
        profile_data = UserProfileCreate(
            bio='Test bio',
            location='Test City'
        )
        
        # Create profile for user
        profile = await user_profile_crud.create_model(
            db_session, 
            profile_data, 
            user_id=basic_data['users'][0].id,
            commit=True
        )
        
        assert profile.user_id == basic_data['users'][0].id
        assert profile.bio == 'Test bio'

    async def test_one_to_one_uniqueness(self, db_session: AsyncSession, basic_data):
        """Test one-to-one relationship uniqueness."""
        user_profile_crud = CRUDPlus(UserProfile)
        
        # Create first profile
        profile_data1 = UserProfileCreate(bio='First bio')
        await user_profile_crud.create_model(
            db_session, 
            profile_data1, 
            user_id=basic_data['users'][0].id,
            commit=True
        )
        
        # Try to create second profile for same user
        profile_data2 = UserProfileCreate(bio='Second bio')
        with pytest.raises(IntegrityError):
            await user_profile_crud.create_model(
                db_session, 
                profile_data2, 
                user_id=basic_data['users'][0].id,
                commit=True
            )


class TestSelfReferencingIntegrity:
    """Test self-referencing relationship integrity."""

    async def test_category_hierarchy_creation(self, db_session: AsyncSession, category_crud: CRUDPlus[Category], basic_data):
        """Test creating category hierarchy."""
        # Create child category
        child_category_data = CategoryCreate(
            name='Programming',
            description='Programming tutorials',
            parent_id=basic_data['categories'][0].id  # Technology category
        )
        
        child_category = await category_crud.create_model(db_session, child_category_data, commit=True)
        
        assert child_category.parent_id == basic_data['categories'][0].id
        assert child_category.name == 'Programming'

    async def test_invalid_self_reference(self, db_session: AsyncSession, category_crud: CRUDPlus[Category], basic_data):
        """Test invalid self-reference (category cannot be its own parent)."""
        category_id = basic_data['categories'][0].id
        
        # Try to make category its own parent
        with pytest.raises(Exception):  # This should be prevented by application logic
            await category_crud.update_model(
                db_session,
                category_id,
                {'parent_id': category_id},
                commit=True
            )

    async def test_employee_supervisor_hierarchy(self, db_session: AsyncSession, employee_crud: CRUDPlus[Employee], department_crud: CRUDPlus[Department]):
        """Test employee supervisor hierarchy."""
        # Create department
        dept_data = DepartmentCreate(name='Engineering')
        dept = await department_crud.create_model(db_session, dept_data, commit=True)
        
        # Create supervisor
        supervisor_data = EmployeeCreate(
            name='John Supervisor',
            email='<EMAIL>',
            department_id=dept.id,
            salary=90000
        )
        supervisor = await employee_crud.create_model(db_session, supervisor_data, commit=True)
        
        # Create subordinate
        subordinate_data = EmployeeCreate(
            name='Jane Employee',
            email='<EMAIL>',
            department_id=dept.id,
            supervisor_id=supervisor.id,
            salary=70000
        )
        subordinate = await employee_crud.create_model(db_session, subordinate_data, commit=True)
        
        assert subordinate.supervisor_id == supervisor.id
        assert subordinate.department_id == dept.id


class TestManyToManyIntegrity:
    """Test many-to-many relationship integrity."""

    async def test_user_role_association(self, db_session: AsyncSession, basic_data):
        """Test user-role many-to-many association."""
        role_crud = CRUDPlus(Role)
        
        # Create roles
        admin_role = await role_crud.create_model(
            db_session, 
            RoleCreate(name='admin', description='Administrator'),
            commit=True
        )
        editor_role = await role_crud.create_model(
            db_session, 
            RoleCreate(name='editor', description='Editor'),
            commit=True
        )
        
        # Create associations
        user_id = basic_data['users'][0].id
        await db_session.execute(
            user_role_table.insert().values([
                {'user_id': user_id, 'role_id': admin_role.id},
                {'user_id': user_id, 'role_id': editor_role.id},
            ])
        )
        await db_session.commit()
        
        # Verify associations
        user_crud = CRUDPlus(User)
        user = await user_crud.select_model(
            db_session,
            user_id,
            options=[selectinload(User.roles)]
        )
        
        assert len(user.roles) == 2
        role_names = [role.name for role in user.roles]
        assert 'admin' in role_names
        assert 'editor' in role_names

    async def test_duplicate_association_prevention(self, db_session: AsyncSession, basic_data):
        """Test prevention of duplicate many-to-many associations."""
        role_crud = CRUDPlus(Role)
        
        # Create role
        role = await role_crud.create_model(
            db_session, 
            RoleCreate(name='test_role'),
            commit=True
        )
        
        user_id = basic_data['users'][0].id
        
        # Create first association
        await db_session.execute(
            user_role_table.insert().values({'user_id': user_id, 'role_id': role.id})
        )
        await db_session.commit()
        
        # Try to create duplicate association
        with pytest.raises(IntegrityError):
            await db_session.execute(
                user_role_table.insert().values({'user_id': user_id, 'role_id': role.id})
            )
            await db_session.commit()


class TestTransactionIntegrity:
    """Test transaction integrity with relationships."""

    async def test_rollback_on_error(self, db_session: AsyncSession, user_crud: CRUDPlus[User], post_crud: CRUDPlus[Post], basic_data):
        """Test transaction rollback on error."""
        initial_user_count = await user_crud.count(db_session)
        initial_post_count = await post_crud.count(db_session)
        
        try:
            async with db_session.begin():
                # Create valid user
                user_data = UserCreate(username='new_user', email='<EMAIL>')
                new_user = await user_crud.create_model(db_session, user_data)
                
                # Try to create post with invalid foreign key
                post_data = PostCreate(
                    title='Test Post',
                    content='Test content',
                    author_id=999,  # Invalid author_id
                    category_id=basic_data['categories'][0].id
                )
                await post_crud.create_model(db_session, post_data)
                
        except IntegrityError:
            # Transaction should be rolled back
            pass
        
        # Verify rollback - counts should be unchanged
        final_user_count = await user_crud.count(db_session)
        final_post_count = await post_crud.count(db_session)
        
        assert final_user_count == initial_user_count
        assert final_post_count == initial_post_count

    async def test_partial_commit_integrity(self, db_session: AsyncSession, user_crud: CRUDPlus[User], basic_data):
        """Test partial commit integrity."""
        # Create user without committing
        user_data = UserCreate(username='temp_user', email='<EMAIL>')
        new_user = await user_crud.create_model(db_session, user_data, flush=True)
        
        # User should exist in session but not committed
        assert new_user.id is not None
        
        # Rollback session
        await db_session.rollback()
        
        # User should not exist after rollback
        users = await user_crud.select_models(db_session, username='temp_user')
        assert len(users) == 0


class TestDataConsistency:
    """Test data consistency across relationships."""

    async def test_relationship_data_consistency(self, db_session: AsyncSession, user_crud: CRUDPlus[User], post_crud: CRUDPlus[Post], basic_data):
        """Test data consistency across relationships."""
        # Get user with posts
        user = await user_crud.select_model(
            db_session,
            basic_data['users'][0].id,
            options=[selectinload(User.posts)]
        )
        
        # Verify consistency
        for post in user.posts:
            assert post.author_id == user.id
            
            # Get post separately and verify author
            post_with_author = await post_crud.select_model(
                db_session,
                post.id,
                options=[selectinload(Post.author)]
            )
            assert post_with_author.author.id == user.id

    async def test_cascade_update_consistency(self, db_session: AsyncSession, user_crud: CRUDPlus[User], post_crud: CRUDPlus[Post], basic_data):
        """Test cascade update consistency."""
        user_id = basic_data['users'][0].id
        
        # Update user
        await user_crud.update_model(
            db_session,
            user_id,
            {'full_name': 'Updated Name'},
            commit=True
        )
        
        # Verify posts still reference correct user
        posts = await post_crud.select_models(
            db_session,
            author_id=user_id,
            options=[selectinload(Post.author)]
        )
        
        for post in posts:
            assert post.author.id == user_id
            assert post.author.full_name == 'Updated Name'
