#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from datetime import datetime
from typing import Optional

from pydantic import BaseModel, EmailStr


# ==================== User Schemas ====================

class UserCreate(BaseModel):
    username: str
    email: EmailStr
    is_active: bool = True


class UserUpdate(BaseModel):
    username: Optional[str] = None
    email: Optional[EmailStr] = None
    is_active: Optional[bool] = None


class UserProfileCreate(BaseModel):
    bio: Optional[str] = None
    location: Optional[str] = None


class UserProfileUpdate(BaseModel):
    bio: Optional[str] = None
    location: Optional[str] = None


# ==================== Category Schemas ====================

class CategoryCreate(BaseModel):
    name: str
    parent_id: Optional[int] = None


class CategoryUpdate(BaseModel):
    name: Optional[str] = None
    parent_id: Optional[int] = None


# ==================== Post Schemas ====================

class PostCreate(BaseModel):
    title: str
    content: str
    author_id: int
    category_id: Optional[int] = None
    is_published: bool = False


class PostUpdate(BaseModel):
    title: Optional[str] = None
    content: Optional[str] = None
    category_id: Optional[int] = None
    is_published: Optional[bool] = None
    view_count: Optional[int] = None


# ==================== Role Schemas ====================

class RoleCreate(BaseModel):
    name: str


class RoleUpdate(BaseModel):
    name: Optional[str] = None


# ==================== Response Schemas ====================

class UserResponse(BaseModel):
    id: int
    username: str
    email: str
    is_active: bool
    created_time: datetime
    updated_time: Optional[datetime]

    class Config:
        from_attributes = True


class PostResponse(BaseModel):
    id: int
    title: str
    content: str
    author_id: int
    category_id: Optional[int]
    is_published: bool
    view_count: int
    created_time: datetime

    class Config:
        from_attributes = True


class CategoryResponse(BaseModel):
    id: int
    name: str
    parent_id: Optional[int]
    created_time: datetime

    class Config:
        from_attributes = True


class RoleResponse(BaseModel):
    id: int
    name: str
    created_time: datetime

    class Config:
        from_attributes = True


class UserProfileResponse(BaseModel):
    id: int
    user_id: int
    bio: Optional[str]
    location: Optional[str]
    created_time: datetime

    class Config:
        from_attributes = True
