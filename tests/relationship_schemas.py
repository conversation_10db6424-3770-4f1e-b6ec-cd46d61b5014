#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from datetime import datetime
from typing import Optional

from pydantic import BaseModel, EmailStr


# ==================== User Schemas ====================

class UserCreate(BaseModel):
    username: str
    email: EmailStr
    full_name: Optional[str] = None
    is_active: bool = True


class UserUpdate(BaseModel):
    username: Optional[str] = None
    email: Optional[EmailStr] = None
    full_name: Optional[str] = None
    is_active: Optional[bool] = None


class UserProfileCreate(BaseModel):
    bio: Optional[str] = None
    avatar_url: Optional[str] = None
    website: Optional[str] = None
    location: Optional[str] = None
    birth_date: Optional[datetime] = None


class UserProfileUpdate(BaseModel):
    bio: Optional[str] = None
    avatar_url: Optional[str] = None
    website: Optional[str] = None
    location: Optional[str] = None
    birth_date: Optional[datetime] = None


# ==================== Category Schemas ====================

class CategoryCreate(BaseModel):
    name: str
    description: Optional[str] = None
    parent_id: Optional[int] = None
    is_active: bool = True


class CategoryUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    parent_id: Optional[int] = None
    is_active: Optional[bool] = None


# ==================== Post Schemas ====================

class PostCreate(BaseModel):
    title: str
    content: str
    author_id: int
    category_id: Optional[int] = None
    is_published: bool = False


class PostUpdate(BaseModel):
    title: Optional[str] = None
    content: Optional[str] = None
    category_id: Optional[int] = None
    is_published: Optional[bool] = None
    view_count: Optional[int] = None


# ==================== Comment Schemas ====================

class CommentCreate(BaseModel):
    content: str
    author_id: int
    post_id: int
    parent_id: Optional[int] = None
    is_approved: bool = True


class CommentUpdate(BaseModel):
    content: Optional[str] = None
    is_approved: Optional[bool] = None


# ==================== Role Schemas ====================

class RoleCreate(BaseModel):
    name: str
    description: Optional[str] = None
    is_active: bool = True


class RoleUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    is_active: Optional[bool] = None


# ==================== Tag Schemas ====================

class TagCreate(BaseModel):
    name: str
    color: Optional[str] = None
    is_active: bool = True


class TagUpdate(BaseModel):
    name: Optional[str] = None
    color: Optional[str] = None
    is_active: Optional[bool] = None


# ==================== Department Schemas ====================

class DepartmentCreate(BaseModel):
    name: str
    description: Optional[str] = None
    manager_id: Optional[int] = None


class DepartmentUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    manager_id: Optional[int] = None


# ==================== Employee Schemas ====================

class EmployeeCreate(BaseModel):
    name: str
    email: EmailStr
    department_id: Optional[int] = None
    supervisor_id: Optional[int] = None
    salary: Optional[int] = None
    hire_date: Optional[datetime] = None


class EmployeeUpdate(BaseModel):
    name: Optional[str] = None
    email: Optional[EmailStr] = None
    department_id: Optional[int] = None
    supervisor_id: Optional[int] = None
    salary: Optional[int] = None
    hire_date: Optional[datetime] = None


# ==================== Response Schemas ====================

class UserResponse(BaseModel):
    id: int
    username: str
    email: str
    full_name: Optional[str]
    is_active: bool
    created_at: datetime
    updated_at: Optional[datetime]

    class Config:
        from_attributes = True


class PostResponse(BaseModel):
    id: int
    title: str
    content: str
    author_id: int
    category_id: Optional[int]
    is_published: bool
    view_count: int
    created_at: datetime
    updated_at: Optional[datetime]

    class Config:
        from_attributes = True


class CommentResponse(BaseModel):
    id: int
    content: str
    author_id: int
    post_id: int
    parent_id: Optional[int]
    is_approved: bool
    created_at: datetime
    updated_at: Optional[datetime]

    class Config:
        from_attributes = True


class CategoryResponse(BaseModel):
    id: int
    name: str
    description: Optional[str]
    parent_id: Optional[int]
    is_active: bool
    created_at: datetime
    updated_at: Optional[datetime]

    class Config:
        from_attributes = True


class RoleResponse(BaseModel):
    id: int
    name: str
    description: Optional[str]
    is_active: bool
    created_at: datetime

    class Config:
        from_attributes = True


class TagResponse(BaseModel):
    id: int
    name: str
    color: Optional[str]
    is_active: bool
    created_at: datetime

    class Config:
        from_attributes = True


class DepartmentResponse(BaseModel):
    id: int
    name: str
    description: Optional[str]
    manager_id: Optional[int]
    created_at: datetime

    class Config:
        from_attributes = True


class EmployeeResponse(BaseModel):
    id: int
    name: str
    email: str
    department_id: Optional[int]
    supervisor_id: Optional[int]
    salary: Optional[int]
    hire_date: datetime
    created_at: datetime

    class Config:
        from_attributes = True


# ==================== Nested Response Schemas ====================

class UserWithProfileResponse(UserResponse):
    profile: Optional["UserProfileResponse"] = None


class UserProfileResponse(BaseModel):
    id: int
    user_id: int
    bio: Optional[str]
    avatar_url: Optional[str]
    website: Optional[str]
    location: Optional[str]
    birth_date: Optional[datetime]
    created_at: datetime
    updated_at: Optional[datetime]

    class Config:
        from_attributes = True


class PostWithAuthorResponse(PostResponse):
    author: Optional[UserResponse] = None


class PostWithCategoryResponse(PostResponse):
    category: Optional[CategoryResponse] = None


class CommentWithAuthorResponse(CommentResponse):
    author: Optional[UserResponse] = None


class CategoryWithParentResponse(CategoryResponse):
    parent: Optional[CategoryResponse] = None
    children: list[CategoryResponse] = []


class DepartmentWithManagerResponse(DepartmentResponse):
    manager: Optional["EmployeeResponse"] = None
    employees: list["EmployeeResponse"] = []


class EmployeeWithDepartmentResponse(EmployeeResponse):
    department: Optional[DepartmentResponse] = None
    supervisor: Optional["EmployeeResponse"] = None
    subordinates: list["EmployeeResponse"] = []
