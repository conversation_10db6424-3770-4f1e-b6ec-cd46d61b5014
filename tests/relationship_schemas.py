#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Pydantic schemas for relationship testing models.
"""
from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel, EmailStr, Field


# ==================== Base Schemas ====================

class BaseSchema(BaseModel):
    """Base schema with common configuration."""
    
    class Config:
        from_attributes = True
        arbitrary_types_allowed = True


# ==================== User Schemas ====================

class UserCreateSchema(BaseSchema):
    """Schema for creating a user."""
    username: str = Field(..., min_length=3, max_length=50)
    email: EmailStr
    full_name: Optional[str] = Field(None, max_length=100)
    is_active: bool = True


class UserUpdateSchema(BaseSchema):
    """Schema for updating a user."""
    username: Optional[str] = Field(None, min_length=3, max_length=50)
    email: Optional[EmailStr] = None
    full_name: Optional[str] = Field(None, max_length=100)
    is_active: Optional[bool] = None


class UserResponseSchema(BaseSchema):
    """Schema for user response."""
    id: int
    username: str
    email: str
    full_name: Optional[str]
    is_active: bool
    created_at: datetime


# ==================== UserProfile Schemas ====================

class UserProfileCreateSchema(BaseSchema):
    """Schema for creating a user profile."""
    bio: Optional[str] = None
    avatar_url: Optional[str] = None
    birth_date: Optional[datetime] = None
    location: Optional[str] = Field(None, max_length=100)
    website: Optional[str] = Field(None, max_length=255)


class UserProfileUpdateSchema(BaseSchema):
    """Schema for updating a user profile."""
    bio: Optional[str] = None
    avatar_url: Optional[str] = None
    birth_date: Optional[datetime] = None
    location: Optional[str] = Field(None, max_length=100)
    website: Optional[str] = Field(None, max_length=255)


class UserProfileResponseSchema(BaseSchema):
    """Schema for user profile response."""
    id: int
    user_id: int
    bio: Optional[str]
    avatar_url: Optional[str]
    birth_date: Optional[datetime]
    location: Optional[str]
    website: Optional[str]


# ==================== Category Schemas ====================

class CategoryCreateSchema(BaseSchema):
    """Schema for creating a category."""
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = None
    parent_id: Optional[int] = None
    is_active: bool = True


class CategoryUpdateSchema(BaseSchema):
    """Schema for updating a category."""
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = None
    parent_id: Optional[int] = None
    is_active: Optional[bool] = None


class CategoryResponseSchema(BaseSchema):
    """Schema for category response."""
    id: int
    name: str
    description: Optional[str]
    parent_id: Optional[int]
    is_active: bool
    created_at: datetime


# ==================== Post Schemas ====================

class PostCreateSchema(BaseSchema):
    """Schema for creating a post."""
    title: str = Field(..., min_length=1, max_length=200)
    content: str = Field(..., min_length=1)
    category_id: Optional[int] = None
    is_published: bool = False


class PostUpdateSchema(BaseSchema):
    """Schema for updating a post."""
    title: Optional[str] = Field(None, min_length=1, max_length=200)
    content: Optional[str] = Field(None, min_length=1)
    category_id: Optional[int] = None
    is_published: Optional[bool] = None
    published_at: Optional[datetime] = None


class PostResponseSchema(BaseSchema):
    """Schema for post response."""
    id: int
    title: str
    content: str
    author_id: int
    category_id: Optional[int]
    is_published: bool
    published_at: Optional[datetime]
    created_at: datetime
    updated_at: Optional[datetime]


# ==================== Comment Schemas ====================

class CommentCreateSchema(BaseSchema):
    """Schema for creating a comment."""
    content: str = Field(..., min_length=1)
    post_id: int
    parent_id: Optional[int] = None
    is_approved: bool = True


class CommentUpdateSchema(BaseSchema):
    """Schema for updating a comment."""
    content: Optional[str] = Field(None, min_length=1)
    is_approved: Optional[bool] = None


class CommentResponseSchema(BaseSchema):
    """Schema for comment response."""
    id: int
    content: str
    author_id: int
    post_id: int
    parent_id: Optional[int]
    is_approved: bool
    created_at: datetime


# ==================== Role Schemas ====================

class RoleCreateSchema(BaseSchema):
    """Schema for creating a role."""
    name: str = Field(..., min_length=1, max_length=50)
    description: Optional[str] = Field(None, max_length=200)
    is_active: bool = True


class RoleUpdateSchema(BaseSchema):
    """Schema for updating a role."""
    name: Optional[str] = Field(None, min_length=1, max_length=50)
    description: Optional[str] = Field(None, max_length=200)
    is_active: Optional[bool] = None


class RoleResponseSchema(BaseSchema):
    """Schema for role response."""
    id: int
    name: str
    description: Optional[str]
    is_active: bool
    created_at: datetime


# ==================== Tag Schemas ====================

class TagCreateSchema(BaseSchema):
    """Schema for creating a tag."""
    name: str = Field(..., min_length=1, max_length=50)
    color: Optional[str] = Field(None, regex=r'^#[0-9A-Fa-f]{6}$')
    is_active: bool = True


class TagUpdateSchema(BaseSchema):
    """Schema for updating a tag."""
    name: Optional[str] = Field(None, min_length=1, max_length=50)
    color: Optional[str] = Field(None, regex=r'^#[0-9A-Fa-f]{6}$')
    is_active: Optional[bool] = None


class TagResponseSchema(BaseSchema):
    """Schema for tag response."""
    id: int
    name: str
    color: Optional[str]
    is_active: bool
    created_at: datetime


# ==================== Nested Response Schemas ====================

class UserWithProfileSchema(UserResponseSchema):
    """User schema with profile included."""
    profile: Optional[UserProfileResponseSchema] = None


class UserWithPostsSchema(UserResponseSchema):
    """User schema with posts included."""
    posts: List[PostResponseSchema] = []


class PostWithAuthorSchema(PostResponseSchema):
    """Post schema with author included."""
    author: UserResponseSchema


class PostWithCategorySchema(PostResponseSchema):
    """Post schema with category included."""
    category: Optional[CategoryResponseSchema] = None


class PostWithTagsSchema(PostResponseSchema):
    """Post schema with tags included."""
    tags: List[TagResponseSchema] = []


class PostWithCommentsSchema(PostResponseSchema):
    """Post schema with comments included."""
    comments: List[CommentResponseSchema] = []


class CategoryWithChildrenSchema(CategoryResponseSchema):
    """Category schema with children included."""
    children: List["CategoryWithChildrenSchema"] = []


class CommentWithRepliesSchema(CommentResponseSchema):
    """Comment schema with replies included."""
    replies: List["CommentWithRepliesSchema"] = []


# Update forward references
CategoryWithChildrenSchema.model_rebuild()
CommentWithRepliesSchema.model_rebuild()
