#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Integration tests for complete CRUD workflows in SQLAlchemy CRUD Plus.
"""
import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from sqlalchemy_crud_plus import CRUDPlus
from tests.model import Ins, InsPks
from tests.schema import ModelTest, ModelTestPks


class TestCompleteWorkflows:
    """Test complete CRUD workflows from start to finish."""

    @pytest.mark.asyncio
    async def test_full_crud_lifecycle(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test complete CRUD lifecycle: Create -> Read -> Update -> Delete."""
        # CREATE
        async with db_session.begin():
            data = ModelTest(name='lifecycle_test')
            created_item = await crud_ins.create_model(db_session, data)
            item_id = created_item.id
        
        # READ
        retrieved_item = await crud_ins.select_model(db_session, item_id)
        assert retrieved_item is not None
        assert retrieved_item.name == 'lifecycle_test'
        
        # UPDATE
        async with db_session.begin():
            update_result = await crud_ins.update_model(
                db_session,
                item_id,
                {'name': 'lifecycle_updated'}
            )
            assert update_result == 1
        
        # Verify update
        updated_item = await crud_ins.select_model(db_session, item_id)
        assert updated_item.name == 'lifecycle_updated'
        
        # DELETE
        async with db_session.begin():
            delete_result = await crud_ins.delete_model(db_session, item_id)
            assert delete_result == 1
        
        # Verify deletion
        deleted_item = await crud_ins.select_model(db_session, item_id)
        assert deleted_item is None

    @pytest.mark.asyncio
    async def test_bulk_operations_workflow(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test bulk operations workflow."""
        # Bulk CREATE
        async with db_session.begin():
            test_data = [ModelTest(name=f'bulk_workflow_{i}') for i in range(1, 6)]
            created_items = await crud_ins.create_models(db_session, test_data)
            assert len(created_items) == 5
        
        # Bulk READ
        all_items = await crud_ins.select_models(db_session, name__startswith='bulk_workflow_')
        assert len(all_items) == 5
        
        # Bulk UPDATE
        async with db_session.begin():
            update_result = await crud_ins.update_model_by_column(
                db_session,
                {'name': 'bulk_updated'},
                allow_multiple=True,
                name__startswith='bulk_workflow_'
            )
            assert update_result == 5
        
        # Verify bulk update
        updated_items = await crud_ins.select_models(db_session, name='bulk_updated')
        assert len(updated_items) == 5
        
        # Bulk DELETE
        async with db_session.begin():
            delete_result = await crud_ins.delete_model_by_column(
                db_session,
                allow_multiple=True,
                name='bulk_updated'
            )
            assert delete_result == 5
        
        # Verify bulk deletion
        remaining_items = await crud_ins.select_models(db_session, name='bulk_updated')
        assert len(remaining_items) == 0

    @pytest.mark.asyncio
    async def test_logical_deletion_workflow(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test logical deletion workflow."""
        # Create items
        async with db_session.begin():
            test_data = [ModelTest(name=f'logical_test_{i}') for i in range(1, 4)]
            created_items = await crud_ins.create_models(db_session, test_data)
        
        # Logical deletion
        async with db_session.begin():
            delete_result = await crud_ins.delete_model_by_column(
                db_session,
                logical_deletion=True,
                allow_multiple=True,
                name__startswith='logical_test_'
            )
            assert delete_result == 3
        
        # Verify items still exist but marked as deleted
        all_items = await crud_ins.select_models(db_session, name__startswith='logical_test_')
        assert len(all_items) == 3
        for item in all_items:
            assert item.del_flag is True
        
        # Count active items (not deleted)
        active_count = await crud_ins.count(db_session, name__startswith='logical_test_', del_flag=False)
        assert active_count == 0
        
        # Count all items (including deleted)
        total_count = await crud_ins.count(db_session, name__startswith='logical_test_')
        assert total_count == 3

    @pytest.mark.asyncio
    async def test_composite_key_workflow(self, db_session: AsyncSession, crud_ins_pks: CRUDPlus[InsPks]):
        """Test complete workflow with composite primary keys."""
        # CREATE
        async with db_session.begin():
            data = ModelTestPks(id=500, name='composite_workflow', sex='men')
            created_item = await crud_ins_pks.create_model(db_session, data)
            composite_key = (created_item.id, created_item.sex)
        
        # READ
        retrieved_item = await crud_ins_pks.select_model(db_session, composite_key)
        assert retrieved_item is not None
        assert retrieved_item.name == 'composite_workflow'
        
        # UPDATE
        async with db_session.begin():
            update_result = await crud_ins_pks.update_model(
                db_session,
                composite_key,
                {'name': 'composite_updated'}
            )
            assert update_result == 1
        
        # Verify update
        updated_item = await crud_ins_pks.select_model(db_session, composite_key)
        assert updated_item.name == 'composite_updated'
        
        # DELETE
        async with db_session.begin():
            delete_result = await crud_ins_pks.delete_model(db_session, composite_key)
            assert delete_result == 1
        
        # Verify deletion
        deleted_item = await crud_ins_pks.select_model(db_session, composite_key)
        assert deleted_item is None


class TestComplexQueryWorkflows:
    """Test complex query workflows."""

    @pytest.mark.asyncio
    async def test_filtering_and_sorting_workflow(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test complex filtering and sorting workflow."""
        # Setup test data
        async with db_session.begin():
            test_data = [
                ModelTest(name='alpha_item'),
                ModelTest(name='beta_item'),
                ModelTest(name='gamma_item'),
                ModelTest(name='alpha_special'),
                ModelTest(name='beta_special'),
            ]
            await crud_ins.create_models(db_session, test_data, del_flag=False)
            
            # Create some with del_flag=True
            deleted_data = [
                ModelTest(name='alpha_deleted'),
                ModelTest(name='beta_deleted'),
            ]
            await crud_ins.create_models(db_session, deleted_data, del_flag=True)
        
        # Complex filtering: active items starting with 'alpha' or 'beta'
        results = await crud_ins.select_models(
            db_session,
            name__or={'startswith': 'alpha', 'like': 'beta%'},
            del_flag=False
        )
        assert len(results) == 4  # alpha_item, beta_item, alpha_special, beta_special
        
        # Filtering with sorting
        sorted_results = await crud_ins.select_models_order(
            db_session,
            sort_columns='name',
            sort_orders='asc',
            del_flag=False,
            name__contains='_item'
        )
        
        names = [r.name for r in sorted_results]
        expected_order = ['alpha_item', 'beta_item', 'gamma_item']
        assert names == expected_order
        
        # Count with complex filters
        count = await crud_ins.count(
            db_session,
            name__startswith='alpha',
            del_flag=False
        )
        assert count == 2  # alpha_item, alpha_special
        
        # Exists with complex filters
        exists = await crud_ins.exists(
            db_session,
            name__endswith='_special',
            del_flag=False
        )
        assert exists is True

    @pytest.mark.asyncio
    async def test_pagination_workflow(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test pagination-like workflow using sorting and filtering."""
        # Create ordered test data
        async with db_session.begin():
            test_data = [ModelTest(name=f'page_item_{i:03d}') for i in range(1, 26)]  # 25 items
            await crud_ins.create_models(db_session, test_data)
        
        # Simulate pagination by using sorting and limiting results through filtering
        # Page 1: items 1-10
        page1_results = await crud_ins.select_models_order(
            db_session,
            sort_columns='name',
            sort_orders='asc',
            name__like='page_item_0%'  # This will get items 001-009
        )
        assert len(page1_results) == 9
        
        # Page 2: items 10-19
        page2_results = await crud_ins.select_models_order(
            db_session,
            sort_columns='name',
            sort_orders='asc',
            name__like='page_item_01%'  # This will get items 010-019
        )
        assert len(page2_results) == 10
        
        # Verify ordering
        page1_names = [r.name for r in page1_results]
        assert page1_names == sorted(page1_names)


class TestTransactionWorkflows:
    """Test transaction-related workflows."""

    @pytest.mark.asyncio
    async def test_successful_transaction_workflow(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test successful transaction with multiple operations."""
        initial_count = await crud_ins.count(db_session)
        
        async with db_session.begin():
            # Create
            data1 = ModelTest(name='transaction_test_1')
            item1 = await crud_ins.create_model(db_session, data1)
            
            data2 = ModelTest(name='transaction_test_2')
            item2 = await crud_ins.create_model(db_session, data2)
            
            # Update
            await crud_ins.update_model(db_session, item1.id, {'name': 'transaction_updated_1'})
            
            # Delete
            await crud_ins.delete_model(db_session, item2.id)
        
        # Verify final state: +1 item (created 2, deleted 1)
        final_count = await crud_ins.count(db_session)
        assert final_count == initial_count + 1
        
        # Verify the remaining item was updated
        remaining_item = await crud_ins.select_model(db_session, item1.id)
        assert remaining_item.name == 'transaction_updated_1'
        
        # Verify the deleted item is gone
        deleted_item = await crud_ins.select_model(db_session, item2.id)
        assert deleted_item is None

    @pytest.mark.asyncio
    async def test_rollback_transaction_workflow(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test transaction rollback workflow."""
        initial_count = await crud_ins.count(db_session)
        
        try:
            async with db_session.begin():
                # Create
                data = ModelTest(name='rollback_test')
                item = await crud_ins.create_model(db_session, data)
                
                # Update
                await crud_ins.update_model(db_session, item.id, {'name': 'rollback_updated'})
                
                # Force rollback
                raise Exception("Intentional rollback")
        except Exception:
            pass
        
        # Verify rollback: count should be unchanged
        final_count = await crud_ins.count(db_session)
        assert final_count == initial_count
        
        # Verify no items with rollback names exist
        rollback_items = await crud_ins.select_models(db_session, name__contains='rollback')
        assert len(rollback_items) == 0

    @pytest.mark.asyncio
    async def test_nested_transaction_workflow(self, db_session_factory, crud_ins: CRUDPlus[Ins]):
        """Test workflow with multiple separate transactions."""
        # Transaction 1: Create items
        async with db_session_factory() as session1:
            async with session1.begin():
                test_data = [ModelTest(name=f'nested_tx_item_{i}') for i in range(1, 4)]
                await crud_ins.create_models(session1, test_data)
        
        # Transaction 2: Update items
        async with db_session_factory() as session2:
            async with session2.begin():
                update_result = await crud_ins.update_model_by_column(
                    session2,
                    {'name': 'nested_tx_updated'},
                    allow_multiple=True,
                    name__startswith='nested_tx_item_'
                )
                assert update_result == 3
        
        # Transaction 3: Verify and delete
        async with db_session_factory() as session3:
            # Verify updates
            updated_items = await crud_ins.select_models(session3, name='nested_tx_updated')
            assert len(updated_items) == 3
            
            async with session3.begin():
                # Delete all
                delete_result = await crud_ins.delete_model_by_column(
                    session3,
                    allow_multiple=True,
                    name='nested_tx_updated'
                )
                assert delete_result == 3
        
        # Final verification
        async with db_session_factory() as session4:
            remaining_items = await crud_ins.select_models(session4, name__contains='nested_tx')
            assert len(remaining_items) == 0


class TestPerformanceWorkflows:
    """Test performance-related workflows."""

    @pytest.mark.asyncio
    async def test_large_dataset_workflow(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test workflow with large dataset operations."""
        batch_size = 500
        
        # Bulk create large dataset
        async with db_session.begin():
            test_data = [ModelTest(name=f'large_dataset_{i:04d}') for i in range(batch_size)]
            created_items = await crud_ins.create_models(db_session, test_data)
            assert len(created_items) == batch_size
        
        # Query large dataset
        all_items = await crud_ins.select_models(db_session, name__startswith='large_dataset_')
        assert len(all_items) == batch_size
        
        # Count large dataset
        count = await crud_ins.count(db_session, name__startswith='large_dataset_')
        assert count == batch_size
        
        # Exists check on large dataset
        exists = await crud_ins.exists(db_session, name__startswith='large_dataset_')
        assert exists is True
        
        # Bulk update large dataset
        async with db_session.begin():
            update_result = await crud_ins.update_model_by_column(
                db_session,
                {'del_flag': True},
                allow_multiple=True,
                name__startswith='large_dataset_'
            )
            assert update_result == batch_size
        
        # Verify bulk update
        updated_count = await crud_ins.count(db_session, name__startswith='large_dataset_', del_flag=True)
        assert updated_count == batch_size
        
        # Bulk delete large dataset
        async with db_session.begin():
            delete_result = await crud_ins.delete_model_by_column(
                db_session,
                allow_multiple=True,
                name__startswith='large_dataset_'
            )
            assert delete_result == batch_size
        
        # Verify bulk deletion
        final_count = await crud_ins.count(db_session, name__startswith='large_dataset_')
        assert final_count == 0

    @pytest.mark.asyncio
    async def test_concurrent_operations_workflow(self, db_session_factory, crud_ins: CRUDPlus[Ins]):
        """Test concurrent operations workflow."""
        import asyncio
        
        # Setup initial data
        async with db_session_factory() as setup_session:
            async with setup_session.begin():
                test_data = [ModelTest(name=f'concurrent_base_{i}') for i in range(10)]
                await crud_ins.create_models(setup_session, test_data)
        
        async def concurrent_read_operation(session_id: int):
            """Perform concurrent read operations."""
            async with db_session_factory() as session:
                results = await crud_ins.select_models(
                    session, 
                    name__startswith='concurrent_base_'
                )
                return len(results)
        
        async def concurrent_write_operation(session_id: int):
            """Perform concurrent write operations."""
            async with db_session_factory() as session:
                async with session.begin():
                    data = ModelTest(name=f'concurrent_write_{session_id}')
                    await crud_ins.create_model(session, data)
                    return session_id
        
        # Run concurrent operations
        read_tasks = [concurrent_read_operation(i) for i in range(5)]
        write_tasks = [concurrent_write_operation(i) for i in range(3)]
        
        read_results, write_results = await asyncio.gather(
            asyncio.gather(*read_tasks),
            asyncio.gather(*write_tasks)
        )
        
        # Verify results
        assert all(count == 10 for count in read_results)  # All reads should see 10 base items
        assert len(write_results) == 3  # All writes should succeed
        
        # Verify final state
        async with db_session_factory() as final_session:
            base_count = await crud_ins.count(final_session, name__startswith='concurrent_base_')
            write_count = await crud_ins.count(final_session, name__startswith='concurrent_write_')
            
            assert base_count == 10
            assert write_count == 3
