#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from datetime import datetime
from typing import Optional

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>umn, DateTime, Foreign<PERSON>ey, Integer, String, Table, Text
from sqlalchemy.orm import DeclarativeBase, Mapped, MappedAsDataclass, declared_attr, mapped_column, relationship


class RelationshipBase(MappedAsDataclass, DeclarativeBase):
    @declared_attr.directive
    def __tablename__(cls) -> str:
        return cls.__name__.lower()


# Association table for many-to-many relationships
user_role_table = Table(
    'user_roles',
    RelationshipBase.metadata,
    Column('user_id', Integer, ForeignKey('user.id'), primary_key=True),
    Column('role_id', Integer, ForeignKey('role.id'), primary_key=True)
)


class User(RelationshipBase):
    __tablename__ = 'user'

    id: Mapped[int] = mapped_column(init=False, primary_key=True, index=True, autoincrement=True)
    username: Mapped[str] = mapped_column(String(50), unique=True, nullable=False)
    email: Mapped[str] = mapped_column(String(100), unique=True, nullable=False)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True)
    created_time: Mapped[datetime] = mapped_column(DateTime, init=False, default_factory=datetime.utcnow)
    updated_time: Mapped[Optional[datetime]] = mapped_column(DateTime, init=False, onupdate=datetime.utcnow)

    # One-to-one relationship
    profile: Mapped[Optional["UserProfile"]] = relationship(
        "UserProfile", back_populates="user", uselist=False, cascade="all, delete-orphan", init=False
    )

    # One-to-many relationship
    posts: Mapped[list["Post"]] = relationship(
        "Post", back_populates="author", cascade="all, delete-orphan", init=False, default_factory=list
    )

    # Many-to-many relationship
    roles: Mapped[list["Role"]] = relationship(
        "Role", secondary=user_role_table, back_populates="users", init=False, default_factory=list
    )


class UserProfile(RelationshipBase):
    __tablename__ = 'userprofile'

    id: Mapped[int] = mapped_column(init=False, primary_key=True, index=True, autoincrement=True)
    user_id: Mapped[int] = mapped_column(Integer, ForeignKey('user.id'), unique=True, nullable=False)
    bio: Mapped[Optional[str]] = mapped_column(Text, default=None)
    location: Mapped[Optional[str]] = mapped_column(String(100), default=None)
    created_time: Mapped[datetime] = mapped_column(DateTime, init=False, default_factory=datetime.utcnow)

    # One-to-one relationship (back reference)
    user: Mapped["User"] = relationship("User", back_populates="profile", init=False)


class Category(RelationshipBase):
    __tablename__ = 'category'

    id: Mapped[int] = mapped_column(init=False, primary_key=True, index=True, autoincrement=True)
    name: Mapped[str] = mapped_column(String(100), unique=True, nullable=False)
    parent_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey('category.id'), default=None)
    created_time: Mapped[datetime] = mapped_column(DateTime, init=False, default_factory=datetime.utcnow)

    # Self-referential relationship
    parent: Mapped[Optional["Category"]] = relationship(
        "Category", remote_side=[id], back_populates="children", init=False
    )
    children: Mapped[list["Category"]] = relationship(
        "Category", back_populates="parent", cascade="all, delete-orphan", init=False, default_factory=list
    )

    # One-to-many relationship
    posts: Mapped[list["Post"]] = relationship(
        "Post", back_populates="category", init=False, default_factory=list
    )


class Post(RelationshipBase):
    __tablename__ = 'post'

    id: Mapped[int] = mapped_column(init=False, primary_key=True, index=True, autoincrement=True)
    title: Mapped[str] = mapped_column(String(200), nullable=False)
    content: Mapped[str] = mapped_column(Text, nullable=False)
    author_id: Mapped[int] = mapped_column(Integer, ForeignKey('user.id'), nullable=False)
    category_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey('category.id'), default=None)
    is_published: Mapped[bool] = mapped_column(Boolean, default=False)
    view_count: Mapped[int] = mapped_column(Integer, default=0)
    created_time: Mapped[datetime] = mapped_column(DateTime, init=False, default_factory=datetime.utcnow)

    # Many-to-one relationship
    author: Mapped["User"] = relationship("User", back_populates="posts", init=False)
    category: Mapped[Optional["Category"]] = relationship("Category", back_populates="posts", init=False)


class Role(RelationshipBase):
    __tablename__ = 'role'

    id: Mapped[int] = mapped_column(init=False, primary_key=True, index=True, autoincrement=True)
    name: Mapped[str] = mapped_column(String(50), unique=True, nullable=False)
    created_time: Mapped[datetime] = mapped_column(DateTime, init=False, default_factory=datetime.utcnow)

    # Many-to-many relationship (back reference)
    users: Mapped[list["User"]] = relationship(
        "User", secondary=user_role_table, back_populates="roles", init=False, default_factory=list
    )



