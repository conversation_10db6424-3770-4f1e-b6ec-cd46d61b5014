#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Advanced features and complex scenario tests for SQLAlchemy CRUD Plus.
"""
import pytest
from typing import Dict, Any

from sqlalchemy import Column, Integer, String, Boolean, DateTime, ForeignKey, and_, or_
from sqlalchemy.orm import DeclarativeBase, relationship

from sqlalchemy_crud_plus import CRUDPlus
from tests.model import Ins, InsPks
from tests.schema import ModelTest


class TestBase(DeclarativeBase):
    """Base class for advanced test models."""
    pass


class Category(TestBase):
    """Category model for testing relationships."""
    __tablename__ = 'categories'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(50), nullable=False)
    description = Column(String(200))
    is_active = Column(Boolean, default=True)
    
    # Relationship
    items = relationship("Item", back_populates="category")


class Item(TestBase):
    """Item model for testing relationships."""
    __tablename__ = 'items'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)
    category_id = Column(Integer, ForeignKey('categories.id'))
    price = Column(Integer)  # Price in cents
    is_available = Column(Boolean, default=True)
    del_flag = Column(Boolean, default=False)
    
    # Relationship
    category = relationship("Category", back_populates="items")


class TestAdvancedFeatures:
    """Test advanced features and complex scenarios."""

    @pytest.mark.asyncio
    async def test_complex_filter_combinations(self, async_db_session):
        """Test complex filter combinations with multiple operators."""
        async with async_db_session.begin() as session:
            crud = CRUDPlus(Ins)
            
            # Create test data with varied patterns
            test_data = [
                ('alpha_test_001', True),
                ('alpha_test_002', False),
                ('beta_test_001', True),
                ('beta_test_002', False),
                ('gamma_special', True),
                ('delta_normal', False),
            ]
            
            created_records = []
            for name, del_flag in test_data:
                data = ModelTest(name=name)
                record = await crud.create_model(session, data, del_flag=del_flag)
                created_records.append(record)
            
            # Test complex AND conditions
            results = await crud.select_models(
                session,
                name__startswith='alpha_',
                del_flag=True
            )
            assert len(results) == 1
            assert results[0].name == 'alpha_test_001'
            
            # Test IN operator with other conditions
            results = await crud.select_models(
                session,
                name__in=['alpha_test_001', 'beta_test_001', 'gamma_special'],
                del_flag=True
            )
            assert len(results) == 3
            
            # Test BETWEEN with string patterns
            results = await crud.select_models(
                session,
                name__between=['alpha_test_001', 'beta_test_999'],
                del_flag__in=[True, False]
            )
            assert len(results) >= 4  # Should include alpha and beta records
            
            # Test NOT operations
            results = await crud.select_models(
                session,
                name__not_like='%special%',
                del_flag__ne=True
            )
            # Should exclude gamma_special and records with del_flag=True
            assert all('special' not in r.name for r in results)
            assert all(r.del_flag is False for r in results)

    @pytest.mark.asyncio
    async def test_or_filter_complex_scenarios(self, async_db_session):
        """Test complex OR filter scenarios."""
        async with async_db_session.begin() as session:
            crud = CRUDPlus(Ins)
            
            # Create test data
            test_names = ['or_test_1', 'or_test_2', 'or_test_3', 'different_name']
            for name in test_names:
                data = ModelTest(name=name)
                await crud.create_model(session, data)
            
            # Test OR with multiple conditions
            results = await crud.select_models(
                session,
                name__or={
                    'eq': 'or_test_1',
                    'like': '%test_2%',
                    'startswith': 'different_'
                }
            )
            assert len(results) == 3
            
            # Test nested OR conditions
            results = await crud.select_models(
                session,
                __or__=[
                    {'name__eq': 'or_test_1'},
                    {'name__like': '%test_3%'},
                    {'name__startswith': 'different_'}
                ]
            )
            assert len(results) == 3

    @pytest.mark.asyncio
    async def test_arithmetic_operations_complex(self, async_db_session):
        """Test complex arithmetic operations in filters."""
        async with async_db_session.begin() as session:
            crud = CRUDPlus(Ins)
            
            # Create records with specific IDs for arithmetic tests
            test_data = []
            for i in range(1, 11):  # IDs 1-10
                data = ModelTest(name=f'arith_test_{i}')
                record = await crud.create_model(session, data)
                test_data.append(record)
            
            # Test addition: id + 5 = 10 (so id = 5)
            results = await crud.select_models(
                session,
                id__add={'value': 5, 'condition': {'eq': 10}},
                name__startswith='arith_test_'
            )
            assert len(results) == 1
            assert results[0].name == 'arith_test_5'
            
            # Test multiplication: id * 2 > 10 (so id > 5)
            results = await crud.select_models(
                session,
                id__mul={'value': 2, 'condition': {'gt': 10}},
                name__startswith='arith_test_'
            )
            assert len(results) >= 5  # IDs 6-10
            
            # Test division: id / 2 < 3 (so id < 6)
            results = await crud.select_models(
                session,
                id__truediv={'value': 2, 'condition': {'lt': 3}},
                name__startswith='arith_test_'
            )
            assert len(results) >= 5  # IDs 1-5
            
            # Test subtraction with BETWEEN
            results = await crud.select_models(
                session,
                id__sub={'value': 3, 'condition': {'between': [2, 4]}},
                name__startswith='arith_test_'
            )
            # id - 3 between 2 and 4, so id between 5 and 7
            assert len(results) == 3

    @pytest.mark.asyncio
    async def test_sorting_edge_cases(self, async_db_session):
        """Test sorting with edge cases and complex scenarios."""
        async with async_db_session.begin() as session:
            crud = CRUDPlus(Ins)
            
            # Create test data with specific sorting challenges
            test_data = [
                'sort_10_item',
                'sort_2_item',
                'sort_1_item',
                'sort_20_item',
                'Sort_Capital',
                'sort_lowercase',
                'SORT_UPPERCASE',
            ]
            
            for name in test_data:
                data = ModelTest(name=name)
                await crud.create_model(session, data)
            
            # Test case-sensitive sorting
            results = await crud.select_models_order(
                session,
                sort_columns='name',
                sort_orders='asc',
                name__startswith='sort'
            )
            
            # Verify sorting order (case-sensitive)
            names = [r.name for r in results]
            assert names == sorted(names)
            
            # Test multi-column sorting
            results = await crud.select_models_order(
                session,
                sort_columns=['name', 'id'],
                sort_orders=['desc', 'asc'],
                name__contains='sort'
            )
            
            # Should be sorted by name DESC, then by id ASC
            assert len(results) >= len(test_data) - 1  # Excluding 'Sort_Capital'
            
            # Test sorting with filters
            results = await crud.select_models_order(
                session,
                sort_columns='id',
                sort_orders='desc',
                name__like='sort_%'
            )
            
            # Verify descending ID order
            ids = [r.id for r in results]
            assert ids == sorted(ids, reverse=True)

    @pytest.mark.asyncio
    async def test_pagination_simulation(self, async_db_session):
        """Test pagination-like behavior using LIMIT and OFFSET simulation."""
        async with async_db_session.begin() as session:
            crud = CRUDPlus(Ins)
            
            # Create a large dataset
            page_size = 10
            total_records = 50
            
            for i in range(total_records):
                data = ModelTest(name=f'page_test_{i:03d}')
                await crud.create_model(session, data)
        
        # Simulate pagination by using sorting and filtering
        async with async_db_session() as session:
            crud = CRUDPlus(Ins)
            
            # Get total count
            total_count = await crud.count(session, name__startswith='page_test_')
            assert total_count == total_records
            
            # Simulate first page (records 0-9)
            first_page = await crud.select_models_order(
                session,
                sort_columns='name',
                sort_orders='asc',
                name__startswith='page_test_'
            )
            
            # Verify we have all records (SQLite doesn't support LIMIT in our current setup)
            assert len(first_page) == total_records
            
            # Verify sorting
            names = [r.name for r in first_page]
            assert names == sorted(names)

    @pytest.mark.asyncio
    async def test_bulk_operations_with_conditions(self, async_db_session):
        """Test bulk operations with complex conditions."""
        async with async_db_session.begin() as session:
            crud = CRUDPlus(Ins)
            
            # Create test data
            categories = ['A', 'B', 'C']
            for category in categories:
                for i in range(10):
                    data = ModelTest(name=f'bulk_{category}_{i:02d}')
                    await crud.create_model(session, data, del_flag=(i % 2 == 0))
            
            # Bulk update with complex conditions
            update_count = await crud.update_model_by_column(
                session,
                {'name': 'bulk_updated'},
                allow_multiple=True,
                name__like='bulk_A_%',
                del_flag=False
            )
            assert update_count == 5  # 5 records in category A with del_flag=False
            
            # Verify updates
            updated_records = await crud.select_models(
                session,
                name='bulk_updated'
            )
            assert len(updated_records) == 5
            
            # Bulk logical deletion
            delete_count = await crud.delete_model_by_column(
                session,
                allow_multiple=True,
                logical_deletion=True,
                name__like='bulk_B_%',
                del_flag=False
            )
            assert delete_count == 5
            
            # Verify logical deletion
            logically_deleted = await crud.select_models(
                session,
                name__like='bulk_B_%',
                del_flag=True
            )
            assert len(logically_deleted) == 10  # 5 originally + 5 newly deleted

    @pytest.mark.asyncio
    async def test_exists_vs_count_optimization(self, async_db_session):
        """Test exists vs count optimization for different scenarios."""
        async with async_db_session.begin() as session:
            crud = CRUDPlus(Ins)
            
            # Create test data
            for i in range(100):
                data = ModelTest(name=f'exists_test_{i}')
                await crud.create_model(session, data)
        
        async with async_db_session() as session:
            crud = CRUDPlus(Ins)
            
            # Test exists for existing data
            exists_result = await crud.exists(session, name__startswith='exists_test_')
            assert exists_result is True
            
            # Test exists for non-existing data
            exists_result = await crud.exists(session, name__startswith='nonexistent_')
            assert exists_result is False
            
            # Test count for comparison
            count_result = await crud.count(session, name__startswith='exists_test_')
            assert count_result == 100
            
            # Test exists with complex conditions
            exists_complex = await crud.exists(
                session,
                name__like='exists_test_1%',
                del_flag=False
            )
            assert exists_complex is True

    @pytest.mark.asyncio
    async def test_update_with_complex_data_types(self, async_db_session):
        """Test updates with various data types and edge cases."""
        async with async_db_session.begin() as session:
            crud = CRUDPlus(Ins)
            
            # Create initial record
            data = ModelTest(name='complex_update_test')
            record = await crud.create_model(session, data)
            
            # Test updating with dictionary
            dict_update = {
                'name': 'updated_via_dict',
                'del_flag': True
            }
            result = await crud.update_model(session, record.id, dict_update)
            assert result == 1
            
            # Verify update
            updated = await crud.select_model(session, record.id)
            assert updated.name == 'updated_via_dict'
            assert updated.del_flag is True
            
            # Test updating with Pydantic model
            pydantic_update = ModelTest(name='updated_via_pydantic')
            result = await crud.update_model(session, record.id, pydantic_update)
            assert result == 1
            
            # Verify update
            updated = await crud.select_model(session, record.id)
            assert updated.name == 'updated_via_pydantic'
            # del_flag should remain True (not in Pydantic model)
            assert updated.del_flag is True

    @pytest.mark.asyncio
    async def test_error_recovery_and_validation(self, async_db_session):
        """Test error recovery and input validation."""
        crud = CRUDPlus(Ins)
        
        # Test validation errors
        async with async_db_session.begin() as session:
            # Test update without filters
            with pytest.raises(ValueError, match="At least one filter condition"):
                await crud.update_model_by_column(session, {'name': 'test'})
            
            # Test delete without filters
            with pytest.raises(ValueError, match="At least one filter condition"):
                await crud.delete_model_by_column(session)
        
        # Test recovery from failed operations
        async with async_db_session.begin() as session:
            # Create a record
            data = ModelTest(name='recovery_test')
            record = await crud.create_model(session, data)
            
            # Attempt invalid update (should not affect the record)
            try:
                await crud.update_model_by_column(session, {'name': 'failed_update'})
            except ValueError:
                pass
            
            # Verify record is unchanged
            unchanged = await crud.select_model(session, record.id)
            assert unchanged.name == 'recovery_test'

    @pytest.mark.asyncio
    async def test_composite_key_advanced_operations(self, create_test_model_pks, async_db_session):
        """Test advanced operations with composite primary keys."""
        async with async_db_session.begin() as session:
            crud = CRUDPlus(InsPks)
            
            # Test bulk operations with composite keys
            update_data = ModelTestPks(name='bulk_updated_composite')
            
            # Update multiple records by column filter
            result = await crud.update_model_by_column(
                session,
                update_data,
                allow_multiple=True,
                sex='men'
            )
            assert result >= 1
            
            # Verify updates
            updated_records = await crud.select_models(session, sex='men')
            assert all(r.name == 'bulk_updated_composite' for r in updated_records)
            
            # Test logical deletion with composite keys
            delete_result = await crud.delete_model_by_column(
                session,
                allow_multiple=True,
                logical_deletion=True,
                sex='women'
            )
            assert delete_result >= 0  # May be 0 if no women records exist
            
            # Test exists with composite key conditions
            exists_result = await crud.exists(session, sex='men')
            assert exists_result is True
