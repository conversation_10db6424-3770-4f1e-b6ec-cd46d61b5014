#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Performance benchmark tests for SQLAlchemy CRUD Plus.
"""
import asyncio
import time
from typing import List

import pytest
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker

from sqlalchemy_crud_plus import CRUDPlus
from tests.model import Ins
from tests.schema import ModelTest


class TestCreatePerformance:
    """Test performance of create operations."""

    @pytest.mark.asyncio
    async def test_single_create_performance(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test single create operation performance."""
        iterations = 100
        
        start_time = time.perf_counter()
        async with db_session.begin():
            for i in range(iterations):
                data = ModelTest(name=f'single_perf_{i}')
                await crud_ins.create_model(db_session, data)
        end_time = time.perf_counter()
        
        execution_time = end_time - start_time
        avg_time_per_create = execution_time / iterations
        
        assert execution_time < 5.0, f"Single creates took {execution_time:.3f}s"
        assert avg_time_per_create < 0.05, f"Average time per create: {avg_time_per_create:.4f}s"

    @pytest.mark.asyncio
    async def test_bulk_create_performance(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test bulk create operation performance."""
        batch_sizes = [100, 500, 1000]
        
        for batch_size in batch_sizes:
            test_data = [ModelTest(name=f'bulk_perf_{i}') for i in range(batch_size)]
            
            start_time = time.perf_counter()
            async with db_session.begin():
                results = await crud_ins.create_models(db_session, test_data)
            end_time = time.perf_counter()
            
            execution_time = end_time - start_time
            items_per_second = batch_size / execution_time
            
            assert len(results) == batch_size
            assert execution_time < 10.0, f"Batch size {batch_size} took {execution_time:.3f}s"
            assert items_per_second > 50, f"Only {items_per_second:.1f} items/sec for batch size {batch_size}"

    @pytest.mark.asyncio
    async def test_create_with_kwargs_performance(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test create performance with additional kwargs."""
        iterations = 200
        
        start_time = time.perf_counter()
        async with db_session.begin():
            for i in range(iterations):
                data = ModelTest(name=f'kwargs_perf_{i}')
                await crud_ins.create_model(db_session, data, del_flag=(i % 2 == 0))
        end_time = time.perf_counter()
        
        execution_time = end_time - start_time
        assert execution_time < 8.0, f"Create with kwargs took {execution_time:.3f}s"


class TestReadPerformance:
    """Test performance of read operations."""

    @pytest.mark.asyncio
    async def test_select_by_id_performance(self, populated_db: List[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test select by ID performance."""
        iterations = 500
        item_ids = [item.id for item in populated_db[:min(len(populated_db), 10)]]
        
        start_time = time.perf_counter()
        for i in range(iterations):
            item_id = item_ids[i % len(item_ids)]
            result = await crud_ins.select_model(db_session, item_id)
            assert result is not None
        end_time = time.perf_counter()
        
        execution_time = end_time - start_time
        avg_time_per_select = execution_time / iterations
        
        assert execution_time < 3.0, f"ID selects took {execution_time:.3f}s"
        assert avg_time_per_select < 0.01, f"Average time per select: {avg_time_per_select:.4f}s"

    @pytest.mark.asyncio
    async def test_select_with_filters_performance(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test select with filters performance."""
        # Setup test data
        async with db_session.begin():
            test_data = [ModelTest(name=f'filter_perf_{i % 10}') for i in range(1000)]
            await crud_ins.create_models(db_session, test_data)
        
        # Test different filter types
        filter_tests = [
            {'name__eq': 'filter_perf_1'},
            {'name__like': 'filter_perf_%'},
            {'name__startswith': 'filter_perf_'},
            {'name__in': ['filter_perf_1', 'filter_perf_2', 'filter_perf_3']},
            {'id__gt': 100},
            {'del_flag': False},
        ]
        
        for filters in filter_tests:
            start_time = time.perf_counter()
            results = await crud_ins.select_models(db_session, **filters)
            end_time = time.perf_counter()
            
            execution_time = end_time - start_time
            assert execution_time < 1.0, f"Filter {filters} took {execution_time:.3f}s"
            assert len(results) >= 0  # Should return some results

    @pytest.mark.asyncio
    async def test_count_vs_exists_performance(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test count vs exists performance comparison."""
        # Setup test data
        async with db_session.begin():
            test_data = [ModelTest(name=f'count_vs_exists_{i}') for i in range(1000)]
            await crud_ins.create_models(db_session, test_data)
        
        # Test count performance
        start_time = time.perf_counter()
        for _ in range(100):
            count = await crud_ins.count(db_session, name__startswith='count_vs_exists_')
        count_time = time.perf_counter() - start_time
        
        # Test exists performance
        start_time = time.perf_counter()
        for _ in range(100):
            exists = await crud_ins.exists(db_session, name__startswith='count_vs_exists_')
        exists_time = time.perf_counter() - start_time
        
        assert count_time < 5.0, f"Count operations took {count_time:.3f}s"
        assert exists_time < 5.0, f"Exists operations took {exists_time:.3f}s"
        
        # Exists should generally be faster or comparable
        # Note: In small datasets, the difference might not be significant

    @pytest.mark.asyncio
    async def test_sorting_performance(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test sorting performance."""
        # Setup test data with varied content for sorting
        async with db_session.begin():
            test_data = []
            for i in range(500):
                name = f"sort_perf_{i:04d}_{chr(65 + (i % 26))}"
                test_data.append(ModelTest(name=name))
            await crud_ins.create_models(db_session, test_data)
        
        # Test single column sort
        start_time = time.perf_counter()
        results = await crud_ins.select_models_order(
            db_session,
            sort_columns='name',
            sort_orders='asc',
            name__startswith='sort_perf_'
        )
        single_sort_time = time.perf_counter() - start_time
        
        assert len(results) == 500
        assert single_sort_time < 2.0, f"Single column sort took {single_sort_time:.3f}s"
        
        # Test multi-column sort
        start_time = time.perf_counter()
        results = await crud_ins.select_models_order(
            db_session,
            sort_columns=['del_flag', 'name'],
            sort_orders=['asc', 'desc'],
            name__startswith='sort_perf_'
        )
        multi_sort_time = time.perf_counter() - start_time
        
        assert len(results) == 500
        assert multi_sort_time < 3.0, f"Multi-column sort took {multi_sort_time:.3f}s"


class TestUpdatePerformance:
    """Test performance of update operations."""

    @pytest.mark.asyncio
    async def test_single_update_performance(self, populated_db: List[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test single update performance."""
        iterations = 100
        
        start_time = time.perf_counter()
        async with db_session.begin():
            for i in range(min(iterations, len(populated_db))):
                item = populated_db[i]
                await crud_ins.update_model(
                    db_session,
                    item.id,
                    {'name': f'updated_perf_{i}'}
                )
        end_time = time.perf_counter()
        
        execution_time = end_time - start_time
        avg_time_per_update = execution_time / min(iterations, len(populated_db))
        
        assert execution_time < 5.0, f"Single updates took {execution_time:.3f}s"
        assert avg_time_per_update < 0.05, f"Average time per update: {avg_time_per_update:.4f}s"

    @pytest.mark.asyncio
    async def test_bulk_update_performance(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test bulk update performance."""
        # Setup test data
        batch_sizes = [100, 500, 1000]
        
        for batch_size in batch_sizes:
            async with db_session.begin():
                test_data = [ModelTest(name=f'bulk_update_perf_{i}') for i in range(batch_size)]
                await crud_ins.create_models(db_session, test_data)
            
            start_time = time.perf_counter()
            async with db_session.begin():
                result = await crud_ins.update_model_by_column(
                    db_session,
                    {'name': 'bulk_updated_perf'},
                    allow_multiple=True,
                    name__startswith='bulk_update_perf_'
                )
            end_time = time.perf_counter()
            
            execution_time = end_time - start_time
            updates_per_second = batch_size / execution_time
            
            assert result == batch_size
            assert execution_time < 5.0, f"Bulk update of {batch_size} items took {execution_time:.3f}s"
            assert updates_per_second > 100, f"Only {updates_per_second:.1f} updates/sec for batch size {batch_size}"

    @pytest.mark.asyncio
    async def test_update_optimization_performance(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test update performance optimization (skip count when allow_multiple=True)."""
        # Setup test data
        async with db_session.begin():
            test_data = [ModelTest(name=f'opt_perf_{i}') for i in range(200)]
            await crud_ins.create_models(db_session, test_data)
        
        # Test with allow_multiple=True (should skip count check)
        start_time = time.perf_counter()
        async with db_session.begin():
            result = await crud_ins.update_model_by_column(
                db_session,
                {'name': 'optimized_update'},
                allow_multiple=True,
                name__startswith='opt_perf_'
            )
        optimized_time = time.perf_counter() - start_time
        
        assert result == 200
        assert optimized_time < 2.0, f"Optimized update took {optimized_time:.3f}s"


class TestDeletePerformance:
    """Test performance of delete operations."""

    @pytest.mark.asyncio
    async def test_single_delete_performance(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test single delete performance."""
        # Setup test data
        async with db_session.begin():
            test_data = [ModelTest(name=f'single_delete_perf_{i}') for i in range(100)]
            created_items = await crud_ins.create_models(db_session, test_data)
        
        start_time = time.perf_counter()
        async with db_session.begin():
            for item in created_items:
                await crud_ins.delete_model(db_session, item.id)
        end_time = time.perf_counter()
        
        execution_time = end_time - start_time
        avg_time_per_delete = execution_time / len(created_items)
        
        assert execution_time < 5.0, f"Single deletes took {execution_time:.3f}s"
        assert avg_time_per_delete < 0.05, f"Average time per delete: {avg_time_per_delete:.4f}s"

    @pytest.mark.asyncio
    async def test_bulk_delete_performance(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test bulk delete performance."""
        batch_sizes = [100, 500, 1000]
        
        for batch_size in batch_sizes:
            # Setup test data
            async with db_session.begin():
                test_data = [ModelTest(name=f'bulk_delete_perf_{i}') for i in range(batch_size)]
                await crud_ins.create_models(db_session, test_data)
            
            start_time = time.perf_counter()
            async with db_session.begin():
                result = await crud_ins.delete_model_by_column(
                    db_session,
                    allow_multiple=True,
                    name__startswith='bulk_delete_perf_'
                )
            end_time = time.perf_counter()
            
            execution_time = end_time - start_time
            deletes_per_second = batch_size / execution_time
            
            assert result == batch_size
            assert execution_time < 5.0, f"Bulk delete of {batch_size} items took {execution_time:.3f}s"
            assert deletes_per_second > 100, f"Only {deletes_per_second:.1f} deletes/sec for batch size {batch_size}"

    @pytest.mark.asyncio
    async def test_logical_deletion_performance(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test logical deletion performance."""
        # Setup test data
        async with db_session.begin():
            test_data = [ModelTest(name=f'logical_delete_perf_{i}') for i in range(500)]
            await crud_ins.create_models(db_session, test_data)
        
        start_time = time.perf_counter()
        async with db_session.begin():
            result = await crud_ins.delete_model_by_column(
                db_session,
                logical_deletion=True,
                allow_multiple=True,
                name__startswith='logical_delete_perf_'
            )
        end_time = time.perf_counter()
        
        execution_time = end_time - start_time
        logical_deletes_per_second = 500 / execution_time
        
        assert result == 500
        assert execution_time < 3.0, f"Logical deletion took {execution_time:.3f}s"
        assert logical_deletes_per_second > 150, f"Only {logical_deletes_per_second:.1f} logical deletes/sec"


class TestConcurrencyPerformance:
    """Test performance under concurrent access."""

    @pytest.mark.asyncio
    async def test_concurrent_read_performance(self, db_session_factory: async_sessionmaker[AsyncSession], crud_ins: CRUDPlus[Ins]):
        """Test concurrent read performance."""
        # Setup test data
        async with db_session_factory() as session:
            async with session.begin():
                test_data = [ModelTest(name=f'concurrent_read_{i}') for i in range(100)]
                await crud_ins.create_models(session, test_data)
        
        async def read_operation(operation_id: int):
            """Perform a read operation."""
            async with db_session_factory() as session:
                return await crud_ins.select_models(
                    session, 
                    name__startswith='concurrent_read_'
                )
        
        # Test concurrent reads
        concurrent_count = 20
        start_time = time.perf_counter()
        tasks = [read_operation(i) for i in range(concurrent_count)]
        results = await asyncio.gather(*tasks)
        end_time = time.perf_counter()
        
        execution_time = end_time - start_time
        operations_per_second = concurrent_count / execution_time
        
        assert len(results) == concurrent_count
        assert all(len(result) == 100 for result in results)
        assert execution_time < 5.0, f"Concurrent reads took {execution_time:.3f}s"
        assert operations_per_second > 10, f"Only {operations_per_second:.1f} concurrent ops/sec"

    @pytest.mark.asyncio
    async def test_concurrent_write_performance(self, db_session_factory: async_sessionmaker[AsyncSession], crud_ins: CRUDPlus[Ins]):
        """Test concurrent write performance."""
        async def write_operation(operation_id: int):
            """Perform a write operation."""
            async with db_session_factory() as session:
                async with session.begin():
                    data = ModelTest(name=f'concurrent_write_{operation_id}')
                    return await crud_ins.create_model(session, data)
        
        # Test concurrent writes
        concurrent_count = 10
        start_time = time.perf_counter()
        tasks = [write_operation(i) for i in range(concurrent_count)]
        results = await asyncio.gather(*tasks)
        end_time = time.perf_counter()
        
        execution_time = end_time - start_time
        writes_per_second = concurrent_count / execution_time
        
        assert len(results) == concurrent_count
        assert all(result.name.startswith('concurrent_write_') for result in results)
        assert execution_time < 10.0, f"Concurrent writes took {execution_time:.3f}s"
        assert writes_per_second > 5, f"Only {writes_per_second:.1f} concurrent writes/sec"


class TestMemoryPerformance:
    """Test memory efficiency and performance."""

    @pytest.mark.asyncio
    async def test_large_resultset_memory_efficiency(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test memory efficiency with large result sets."""
        try:
            import psutil
            import os
            
            process = psutil.Process(os.getpid())
            initial_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_monitoring = True
        except ImportError:
            memory_monitoring = False
        
        # Create large dataset
        batch_size = 2000
        async with db_session.begin():
            test_data = [ModelTest(name=f'memory_test_{i}') for i in range(batch_size)]
            await crud_ins.create_models(db_session, test_data)
        
        # Query large result set
        start_time = time.perf_counter()
        results = await crud_ins.select_models(db_session, name__startswith='memory_test_')
        end_time = time.perf_counter()
        
        execution_time = end_time - start_time
        
        assert len(results) == batch_size
        assert execution_time < 5.0, f"Large query took {execution_time:.3f}s"
        
        if memory_monitoring:
            final_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_increase = final_memory - initial_memory
            assert memory_increase < 200, f"Memory usage too high: {memory_increase:.2f}MB"

    @pytest.mark.asyncio
    async def test_transaction_performance(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test transaction performance and rollback efficiency."""
        # Test commit performance
        start_time = time.perf_counter()
        async with db_session.begin():
            test_data = [ModelTest(name=f'commit_perf_{i}') for i in range(100)]
            await crud_ins.create_models(db_session, test_data)
        commit_time = time.perf_counter() - start_time
        
        # Test rollback performance
        start_time = time.perf_counter()
        try:
            async with db_session.begin():
                test_data = [ModelTest(name=f'rollback_perf_{i}') for i in range(100)]
                await crud_ins.create_models(db_session, test_data)
                raise Exception("Intentional rollback")
        except Exception:
            pass
        rollback_time = time.perf_counter() - start_time
        
        assert commit_time < 3.0, f"Commit took {commit_time:.3f}s"
        assert rollback_time < 2.0, f"Rollback took {rollback_time:.3f}s"
        
        # Verify rollback worked
        rollback_count = await crud_ins.count(db_session, name__startswith='rollback_perf_')
        assert rollback_count == 0
