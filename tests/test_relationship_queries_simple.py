#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import pytest
import pytest_asyncio
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload, joinedload

from sqlalchemy_crud_plus import CRUDPlus
from tests.relationship_models import User, UserProfile, Post, Category, Role

pytestmark = pytest.mark.asyncio


class TestBasicRelationshipQueries:
    """Test basic relationship loading with options."""

    async def test_select_with_selectinload(self, relationship_session: AsyncSession, user_crud: CRUDPlus[User], sample_data):
        """Test selectinload strategy for one-to-many relationships."""
        users = await user_crud.select_models(
            relationship_session,
            options=[selectinload(User.posts)],
            is_active=True
        )
        
        assert len(users) == 3
        # Check that posts are loaded
        for user in users:
            assert hasattr(user, 'posts')
            # Posts should be loaded without additional queries
            posts_count = len(user.posts)
            assert posts_count >= 0

    async def test_select_with_joinedload(self, relationship_session: AsyncSession, user_crud: CRUDPlus[User], sample_data):
        """Test joinedload strategy for one-to-one relationships."""
        users = await user_crud.select_models(
            relationship_session,
            options=[joinedload(User.profile)]
        )
        
        assert len(users) == 3
        # Check that profiles are loaded for users who have them
        users_with_profiles = [u for u in users if u.profile is not None]
        assert len(users_with_profiles) == 2  # Only first 2 users have profiles

    async def test_select_model_with_joins(self, relationship_session: AsyncSession, user_crud: CRUDPlus[User], sample_data):
        """Test select_model with joins parameter."""
        user_id = sample_data['users'][0].id
        
        user = await user_crud.select_model(
            relationship_session,
            user_id,
            joins=[{'target': 'posts', 'type': 'left'}],
            options=[selectinload(User.posts)]
        )
        
        assert user is not None
        assert user.id == user_id
        assert hasattr(user, 'posts')

    async def test_select_models_with_relationships_method(self, relationship_session: AsyncSession, user_crud: CRUDPlus[User], sample_data):
        """Test the new select_models_with_relationships method."""
        users = await user_crud.select_models_with_relationships(
            relationship_session,
            relationships=['posts', 'profile', 'roles'],
            strategy='selectinload'
        )
        
        assert len(users) == 3
        # Verify all relationships are loaded
        for user in users:
            assert hasattr(user, 'posts')
            assert hasattr(user, 'profile')
            assert hasattr(user, 'roles')

    async def test_select_model_with_relationships_method(self, relationship_session: AsyncSession, user_crud: CRUDPlus[User], sample_data):
        """Test the new select_model_with_relationships method."""
        user_id = sample_data['users'][0].id
        
        user = await user_crud.select_model_with_relationships(
            relationship_session,
            user_id,
            relationships=['posts', 'roles'],
            strategy='selectinload'
        )
        
        assert user is not None
        assert user.id == user_id
        assert hasattr(user, 'posts')
        assert hasattr(user, 'roles')


class TestAdvancedJoinQueries:
    """Test advanced join functionality."""

    async def test_inner_join_query(self, relationship_session: AsyncSession, post_crud: CRUDPlus[Post], sample_data):
        """Test inner join with relationship."""
        posts = await post_crud.select_models(
            relationship_session,
            joins=[{'target': 'author', 'type': 'inner'}],
            options=[selectinload(Post.author)],
            is_published=True
        )
        
        # Should return posts that have authors (all posts should have authors)
        assert len(posts) > 0
        for post in posts:
            assert post.author is not None
            assert post.is_published is True

    async def test_left_join_query(self, relationship_session: AsyncSession, post_crud: CRUDPlus[Post], sample_data):
        """Test left outer join with relationship."""
        posts = await post_crud.select_models(
            relationship_session,
            joins=[{'target': 'category', 'type': 'left'}],
            options=[selectinload(Post.category)]
        )
        
        # Should return all posts, including those without categories
        assert len(posts) == 5
        posts_with_category = [p for p in posts if p.category is not None]
        assert len(posts_with_category) > 0

    async def test_multiple_joins(self, relationship_session: AsyncSession, post_crud: CRUDPlus[Post], sample_data):
        """Test multiple joins in one query."""
        posts = await post_crud.select_models(
            relationship_session,
            joins=[
                {'target': 'author', 'type': 'inner'},
                {'target': 'category', 'type': 'left'}
            ],
            options=[
                selectinload(Post.author),
                selectinload(Post.category)
            ]
        )
        
        assert len(posts) > 0
        for post in posts:
            assert post.author is not None  # Inner join ensures author exists
            # Category may be None due to left join


class TestRelationshipFiltering:
    """Test filtering across relationships."""

    async def test_filter_by_relationship_field(self, relationship_session: AsyncSession, post_crud: CRUDPlus[Post], sample_data):
        """Test filtering posts by author username."""
        posts = await post_crud.select_models(
            relationship_session,
            options=[selectinload(Post.author)],
            author__username='user_1'
        )
        
        assert len(posts) > 0
        for post in posts:
            assert post.author.username == 'user_1'

    async def test_filter_by_relationship_field_with_operator(self, relationship_session: AsyncSession, post_crud: CRUDPlus[Post], sample_data):
        """Test filtering with operators across relationships."""
        posts = await post_crud.select_models(
            relationship_session,
            options=[selectinload(Post.author)],
            author__username__like='user_%'
        )
        
        assert len(posts) > 0
        for post in posts:
            assert post.author.username.startswith('user_')

    async def test_complex_relationship_filtering(self, relationship_session: AsyncSession, user_crud: CRUDPlus[User], sample_data):
        """Test complex filtering across multiple relationships."""
        users = await user_crud.select_models(
            relationship_session,
            options=[selectinload(User.posts)],
            posts__is_published=True,
            posts__view_count__gt=10
        )
        
        # Should return users who have published posts with view_count > 10
        assert len(users) >= 0


class TestSelfReferencingRelationships:
    """Test self-referencing relationships."""

    async def test_category_parent_child(self, relationship_session: AsyncSession, category_crud: CRUDPlus[Category], sample_data):
        """Test querying categories with parent-child relationships."""
        categories = await category_crud.select_models(
            relationship_session,
            options=[selectinload(Category.children), selectinload(Category.parent)]
        )
        
        assert len(categories) == 3
        
        # Find parent categories (those without parent_id)
        parent_categories = [c for c in categories if c.parent_id is None]
        assert len(parent_categories) == 2  # Technology and Science
        
        # Find child categories
        child_categories = [c for c in categories if c.parent_id is not None]
        assert len(child_categories) == 1  # Programming


class TestManyToManyRelationships:
    """Test many-to-many relationships."""

    async def test_user_roles_relationship(self, relationship_session: AsyncSession, user_crud: CRUDPlus[User], sample_data):
        """Test user-roles many-to-many relationship."""
        users = await user_crud.select_models(
            relationship_session,
            options=[selectinload(User.roles)]
        )
        
        assert len(users) == 3
        
        # Check that some users have roles
        users_with_roles = [u for u in users if len(u.roles) > 0]
        assert len(users_with_roles) >= 2  # At least 2 users have roles

    async def test_filter_by_many_to_many_relationship(self, relationship_session: AsyncSession, user_crud: CRUDPlus[User], sample_data):
        """Test filtering by many-to-many relationship."""
        users = await user_crud.select_models(
            relationship_session,
            options=[selectinload(User.roles)],
            roles__name='admin'
        )
        
        # Should return users with admin role
        assert len(users) >= 1
        for user in users:
            role_names = [role.name for role in user.roles]
            assert 'admin' in role_names


class TestAggregationQueries:
    """Test aggregation queries with relationships."""

    async def test_count_with_joins(self, relationship_session: AsyncSession, post_crud: CRUDPlus[Post], sample_data):
        """Test counting with joins."""
        count = await post_crud.count_with_joins(
            relationship_session,
            joins=[{'target': 'author', 'type': 'inner'}],
            is_published=True
        )
        
        assert count >= 0
        assert isinstance(count, int)

    async def test_aggregation_query(self, relationship_session: AsyncSession, post_crud: CRUDPlus[Post], sample_data):
        """Test aggregation queries."""
        results = await post_crud.aggregate(
            relationship_session,
            aggregations={
                'total_posts': 'count',
                'avg_views': ('avg', 'view_count'),
                'max_views': ('max', 'view_count'),
                'min_views': ('min', 'view_count')
            },
            is_published=True
        )
        
        assert len(results) == 1
        result = results[0]
        assert 'total_posts' in result
        assert 'avg_views' in result
        assert 'max_views' in result
        assert 'min_views' in result
        assert result['total_posts'] >= 0


class TestEnhancedSelectMethods:
    """Test enhanced select methods with relationship support."""

    async def test_select_models_order_with_joins(self, relationship_session: AsyncSession, post_crud: CRUDPlus[Post], sample_data):
        """Test select_models_order with joins and options."""
        posts = await post_crud.select_models_order(
            relationship_session,
            sort_columns=['created_time'],
            sort_orders=['desc'],
            joins=[{'target': 'author', 'type': 'inner'}],
            options=[selectinload(Post.author)],
            is_published=True
        )
        
        assert len(posts) >= 0
        # Verify sorting (newer posts first)
        if len(posts) > 1:
            for i in range(len(posts) - 1):
                assert posts[i].created_time >= posts[i + 1].created_time

    async def test_select_model_by_column_with_joins(self, relationship_session: AsyncSession, post_crud: CRUDPlus[Post], sample_data):
        """Test select_model_by_column with joins."""
        post = await post_crud.select_model_by_column(
            relationship_session,
            joins=[{'target': 'author', 'type': 'inner'}],
            options=[selectinload(Post.author)],
            is_published=True
        )
        
        if post:
            assert post.is_published is True
            assert hasattr(post, 'author')
            assert post.author is not None


class TestErrorHandling:
    """Test error handling in relationship queries."""

    async def test_invalid_relationship_name_in_options(self, relationship_session: AsyncSession, user_crud: CRUDPlus[User]):
        """Test error handling for invalid relationship names in options."""
        # This should handle gracefully or raise appropriate error
        try:
            users = await user_crud.select_models(
                relationship_session,
                options=[selectinload('nonexistent_relationship')]
            )
            # If no error is raised, the result should be empty or handle gracefully
            assert isinstance(users, (list, tuple))
        except Exception as e:
            # Should be a specific relationship error
            assert 'relationship' in str(e).lower() or 'attribute' in str(e).lower()

    async def test_invalid_join_configuration(self, relationship_session: AsyncSession, post_crud: CRUDPlus[Post]):
        """Test error handling for invalid join configuration."""
        # This should handle gracefully or raise appropriate error
        try:
            posts = await post_crud.select_models(
                relationship_session,
                joins=[{'target': 'nonexistent_relationship', 'type': 'inner'}]
            )
            # If no error is raised, the result should be empty or handle gracefully
            assert isinstance(posts, (list, tuple))
        except Exception as e:
            # Should be a specific relationship error
            assert 'relationship' in str(e).lower() or 'join' in str(e).lower()
