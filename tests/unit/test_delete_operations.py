#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Unit tests for DELETE operations in SQLAlchemy CRUD Plus.
"""
import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from sqlalchemy_crud_plus import CRUDPlus
from sqlalchemy_crud_plus.errors import MultipleResultsError, ModelColumnError
from tests.model import Ins, InsPks


class TestDeleteByPrimaryKey:
    """Test deleting models by primary key."""

    @pytest.mark.asyncio
    async def test_delete_by_simple_pk(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test deleting model by simple primary key."""
        first_item = populated_db[0]
        
        async with db_session.begin():
            result = await crud_ins.delete_model(db_session, first_item.id)
            assert result == 1
        
        deleted_item = await crud_ins.select_model(db_session, first_item.id)
        assert deleted_item is None

    @pytest.mark.asyncio
    async def test_delete_by_nonexistent_pk(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test deleting model by non-existent primary key."""
        async with db_session.begin():
            result = await crud_ins.delete_model(db_session, 99999)
            assert result == 0

    @pytest.mark.asyncio
    async def test_delete_by_composite_pk(self, populated_db_pks: dict, db_session: AsyncSession, crud_ins_pks: CRUDPlus[InsPks]):
        """Test deleting model by composite primary key."""
        first_woman = populated_db_pks['women'][0]
        
        async with db_session.begin():
            result = await crud_ins_pks.delete_model(db_session, (first_woman.id, first_woman.sex))
            assert result == 1
        
        deleted_item = await crud_ins_pks.select_model(db_session, (first_woman.id, first_woman.sex))
        assert deleted_item is None

    @pytest.mark.asyncio
    async def test_delete_with_flush(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test deleting with flush=True."""
        first_item = populated_db[0]
        
        async with db_session.begin():
            result = await crud_ins.delete_model(db_session, first_item.id, flush=True)
            assert result == 1
            
            # Should not be able to find the item immediately due to flush
            deleted_item = await crud_ins.select_model(db_session, first_item.id)
            assert deleted_item is None

    @pytest.mark.asyncio
    async def test_delete_with_commit(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test deleting with commit=True."""
        first_item = populated_db[0]
        
        result = await crud_ins.delete_model(db_session, first_item.id, commit=True)
        assert result == 1
        
        # Verify persistence
        deleted_item = await crud_ins.select_model(db_session, first_item.id)
        assert deleted_item is None


class TestDeleteByColumn:
    """Test deleting models by column filters."""

    @pytest.mark.asyncio
    async def test_delete_single_by_column(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test deleting single model by column filter."""
        first_item = populated_db[0]
        
        async with db_session.begin():
            result = await crud_ins.delete_model_by_column(
                db_session,
                allow_multiple=False,
                name=first_item.name
            )
            assert result == 1
        
        deleted_item = await crud_ins.select_model(db_session, first_item.id)
        assert deleted_item is None

    @pytest.mark.asyncio
    async def test_delete_multiple_by_column(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test deleting multiple models by column filter."""
        async with db_session.begin():
            result = await crud_ins.delete_model_by_column(
                db_session,
                allow_multiple=True,
                del_flag=True
            )
            assert result > 0
        
        remaining_items = await crud_ins.select_models(db_session, del_flag=True)
        assert len(remaining_items) == 0

    @pytest.mark.asyncio
    async def test_delete_multiple_not_allowed(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test deleting multiple models when not allowed."""
        async with db_session.begin():
            with pytest.raises(MultipleResultsError):
                await crud_ins.delete_model_by_column(
                    db_session,
                    allow_multiple=False,
                    name__startswith='item_'
                )

    @pytest.mark.asyncio
    async def test_delete_with_complex_filters(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test deleting with complex filter conditions."""
        async with db_session.begin():
            result = await crud_ins.delete_model_by_column(
                db_session,
                allow_multiple=True,
                name__startswith='item_',
                del_flag=True
            )
            assert result >= 0  # May be 0 if no items match both conditions

    @pytest.mark.asyncio
    async def test_delete_with_no_matches(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test deleting with filters that match no records."""
        async with db_session.begin():
            result = await crud_ins.delete_model_by_column(
                db_session,
                allow_multiple=True,
                name='nonexistent'
            )
            assert result == 0

    @pytest.mark.asyncio
    async def test_delete_without_filters_raises_error(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test that deleting without filters raises an error."""
        async with db_session.begin():
            with pytest.raises(ValueError, match="At least one filter condition"):
                await crud_ins.delete_model_by_column(db_session)

    @pytest.mark.asyncio
    async def test_delete_with_flush_in_column_delete(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test deleting by column with flush=True."""
        first_item = populated_db[0]
        
        async with db_session.begin():
            result = await crud_ins.delete_model_by_column(
                db_session,
                allow_multiple=False,
                flush=True,
                name=first_item.name
            )
            assert result == 1
            
            # Should not be able to find the item immediately due to flush
            deleted_item = await crud_ins.select_model(db_session, first_item.id)
            assert deleted_item is None


class TestLogicalDeletion:
    """Test logical deletion operations."""

    @pytest.mark.asyncio
    async def test_logical_deletion_basic(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test basic logical deletion."""
        first_item = populated_db[0]
        
        async with db_session.begin():
            result = await crud_ins.delete_model_by_column(
                db_session,
                logical_deletion=True,
                name=first_item.name
            )
            assert result == 1
        
        # Item should still exist but marked as deleted
        item = await crud_ins.select_model(db_session, first_item.id)
        assert item is not None
        assert item.del_flag is True

    @pytest.mark.asyncio
    async def test_logical_deletion_custom_column(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test logical deletion with custom column name."""
        first_item = populated_db[0]
        
        async with db_session.begin():
            result = await crud_ins.delete_model_by_column(
                db_session,
                logical_deletion=True,
                deleted_flag_column='del_flag',
                name=first_item.name
            )
            assert result == 1
        
        # Item should still exist but marked as deleted
        item = await crud_ins.select_model(db_session, first_item.id)
        assert item is not None
        assert item.del_flag is True

    @pytest.mark.asyncio
    async def test_logical_deletion_invalid_column(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test logical deletion with invalid column name."""
        first_item = populated_db[0]
        
        async with db_session.begin():
            with pytest.raises(ModelColumnError):
                await crud_ins.delete_model_by_column(
                    db_session,
                    logical_deletion=True,
                    deleted_flag_column='nonexistent_column',
                    name=first_item.name
                )

    @pytest.mark.asyncio
    async def test_logical_deletion_multiple_records(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test logical deletion of multiple records."""
        async with db_session.begin():
            result = await crud_ins.delete_model_by_column(
                db_session,
                logical_deletion=True,
                allow_multiple=True,
                del_flag=False
            )
            assert result > 0
        
        # All affected items should still exist but marked as deleted
        logically_deleted = await crud_ins.select_models(db_session, del_flag=True)
        assert len(logically_deleted) >= result

    @pytest.mark.asyncio
    async def test_logical_deletion_already_deleted(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test logical deletion of already deleted records."""
        # Find an item that's already marked as deleted
        deleted_items = [item for item in populated_db if item.del_flag is True]
        if deleted_items:
            deleted_item = deleted_items[0]
            
            async with db_session.begin():
                result = await crud_ins.delete_model_by_column(
                    db_session,
                    logical_deletion=True,
                    name=deleted_item.name
                )
                # Should still update the record even if already marked as deleted
                assert result == 1


class TestDeletePerformanceOptimizations:
    """Test performance optimizations in delete operations."""

    @pytest.mark.asyncio
    async def test_delete_skip_count_when_multiple_allowed(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test that count query is skipped when allow_multiple=True."""
        async with db_session.begin():
            # This should skip the count check for better performance
            result = await crud_ins.delete_model_by_column(
                db_session,
                allow_multiple=True,
                del_flag=True
            )
            assert result >= 0

    @pytest.mark.asyncio
    async def test_delete_count_check_when_multiple_not_allowed(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test that count query is performed when allow_multiple=False."""
        first_item = populated_db[0]
        
        async with db_session.begin():
            # This should perform a count check first
            result = await crud_ins.delete_model_by_column(
                db_session,
                allow_multiple=False,
                name=first_item.name
            )
            assert result == 1


class TestDeleteEdgeCases:
    """Test edge cases in delete operations."""

    @pytest.mark.asyncio
    async def test_delete_with_special_character_filters(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test deleting with special character filters."""
        from tests.schema import ModelTest
        
        special_names = ["test'quote", 'test"double', 'test\\backslash']
        
        # Create records with special characters
        async with db_session.begin():
            for name in special_names:
                data = ModelTest(name=name)
                await crud_ins.create_model(db_session, data)
        
        # Test deletion
        for name in special_names:
            result = await crud_ins.delete_model_by_column(
                db_session,
                allow_multiple=False,
                name=name
            )
            assert result == 1

            # Verify deletion
            remaining = await crud_ins.select_models(db_session, name=name)
            assert len(remaining) == 0

    @pytest.mark.asyncio
    async def test_delete_with_empty_string_filter(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test deleting with empty string filter."""
        from tests.schema import ModelTest
        
        # Create record with empty name
        async with db_session.begin():
            data = ModelTest(name='')
            created_item = await crud_ins.create_model(db_session, data)
        
        # Delete by empty string
        async with db_session.begin():
            result = await crud_ins.delete_model_by_column(
                db_session,
                allow_multiple=False,
                name=''
            )
            assert result == 1
        
        # Verify deletion
        deleted_item = await crud_ins.select_model(db_session, created_item.id)
        assert deleted_item is None

    @pytest.mark.asyncio
    async def test_delete_with_null_filter(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test deleting with NULL filter."""
        # This tests the IS NULL filter functionality
        result_count = await crud_ins.delete_model_by_column(
            db_session,
            allow_multiple=True,
            name__is=None
        )
        # Should not raise an error, may return 0 if no NULL names exist
        assert result_count >= 0

    @pytest.mark.asyncio
    async def test_delete_all_with_broad_filter(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test deleting all records with a broad filter."""
        initial_count = len(populated_db)
        
        async with db_session.begin():
            result = await crud_ins.delete_model_by_column(
                db_session,
                allow_multiple=True,
                name__startswith='item_'
            )
            assert result == initial_count
        
        # Verify all matching records are deleted
        remaining = await crud_ins.select_models(db_session, name__startswith='item_')
        assert len(remaining) == 0

    @pytest.mark.asyncio
    async def test_delete_with_complex_or_filters(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test deleting with complex OR filter conditions."""
        async with db_session.begin():
            result = await crud_ins.delete_model_by_column(
                db_session,
                allow_multiple=True,
                name__or={'eq': 'item_1', 'like': 'item_2%'}
            )
            assert result >= 1

    @pytest.mark.asyncio
    async def test_delete_transaction_rollback(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test delete operation rollback on transaction failure."""
        first_item = populated_db[0]
        initial_count = await crud_ins.count(db_session)
        
        try:
            async with db_session.begin():
                await crud_ins.delete_model(db_session, first_item.id)
                # Force a rollback
                raise Exception("Intentional rollback")
        except Exception:
            pass
        
        # Verify rollback worked - item should still exist
        final_count = await crud_ins.count(db_session)
        assert final_count == initial_count
        
        item_still_exists = await crud_ins.select_model(db_session, first_item.id)
        assert item_still_exists is not None
