#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Unit tests for READ operations in SQLAlchemy CRUD Plus.
"""
import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from sqlalchemy_crud_plus import CRUDPlus
from tests.model import Ins, InsPks
from tests.schema import ModelTest


class TestSelectByPrimaryKey:
    """Test selecting models by primary key."""

    @pytest.mark.asyncio
    async def test_select_by_simple_pk(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test selecting model by simple primary key."""
        first_item = populated_db[0]
        result = await crud_ins.select_model(db_session, first_item.id)
        
        assert result is not None
        assert result.id == first_item.id
        assert result.name == first_item.name

    @pytest.mark.asyncio
    async def test_select_by_nonexistent_pk(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test selecting model by non-existent primary key."""
        result = await crud_ins.select_model(db_session, 99999)
        assert result is None

    @pytest.mark.asyncio
    async def test_select_by_composite_pk(self, populated_db_pks: dict, db_session: AsyncSession, crud_ins_pks: CRUDPlus[InsPks]):
        """Test selecting model by composite primary key."""
        first_man = populated_db_pks['men'][0]
        result = await crud_ins_pks.select_model(db_session, (first_man.id, first_man.sex))
        
        assert result is not None
        assert result.id == first_man.id
        assert result.sex == first_man.sex

    @pytest.mark.asyncio
    async def test_select_by_invalid_composite_pk(self, db_session: AsyncSession, crud_ins_pks: CRUDPlus[InsPks]):
        """Test selecting with invalid composite primary key."""
        from sqlalchemy_crud_plus.errors import CompositePrimaryKeysError
        
        # Too few components
        with pytest.raises(CompositePrimaryKeysError):
            await crud_ins_pks.select_model(db_session, (1,))
        
        # Too many components
        with pytest.raises(CompositePrimaryKeysError):
            await crud_ins_pks.select_model(db_session, (1, 'men', 'extra'))

    @pytest.mark.asyncio
    async def test_select_with_additional_filters(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test selecting by primary key with additional WHERE clauses."""
        first_item = populated_db[0]
        
        # Should find the item
        result = await crud_ins.select_model(db_session, first_item.id, first_item.name == first_item.name)
        assert result is not None
        
        # Should not find the item due to conflicting filter
        result = await crud_ins.select_model(db_session, first_item.id, first_item.name == 'nonexistent')
        assert result is None


class TestSelectByColumn:
    """Test selecting models by column filters."""

    @pytest.mark.asyncio
    async def test_select_single_by_column(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test selecting single model by column filter."""
        first_item = populated_db[0]
        result = await crud_ins.select_model_by_column(db_session, name=first_item.name)
        
        assert result is not None
        assert result.name == first_item.name

    @pytest.mark.asyncio
    async def test_select_single_by_nonexistent_column_value(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test selecting by non-existent column value."""
        result = await crud_ins.select_model_by_column(db_session, name='nonexistent')
        assert result is None

    @pytest.mark.asyncio
    async def test_select_multiple_by_column(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test selecting multiple models by column filter."""
        results = await crud_ins.select_models(db_session, del_flag=False)
        assert len(results) > 0
        
        for result in results:
            assert result.del_flag is False

    @pytest.mark.asyncio
    async def test_select_with_string_filters(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test selecting with string-specific filters."""
        # Test LIKE
        results = await crud_ins.select_models(db_session, name__like='item_%')
        assert len(results) == len(populated_db)
        
        # Test startswith
        results = await crud_ins.select_models(db_session, name__startswith='item_')
        assert len(results) == len(populated_db)
        
        # Test endswith
        results = await crud_ins.select_models(db_session, name__endswith='_1')
        assert len(results) >= 1
        
        # Test contains
        results = await crud_ins.select_models(db_session, name__contains='item')
        assert len(results) == len(populated_db)

    @pytest.mark.asyncio
    async def test_select_with_comparison_filters(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test selecting with comparison filters."""
        # Test greater than
        results = await crud_ins.select_models(db_session, id__gt=0)
        assert len(results) >= len(populated_db)
        
        # Test less than
        results = await crud_ins.select_models(db_session, id__lt=1000)
        assert len(results) >= len(populated_db)
        
        # Test not equal
        results = await crud_ins.select_models(db_session, name__ne='nonexistent')
        assert len(results) == len(populated_db)

    @pytest.mark.asyncio
    async def test_select_with_list_filters(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test selecting with list-based filters."""
        # Test IN
        first_three_names = [item.name for item in populated_db[:3]]
        results = await crud_ins.select_models(db_session, name__in=first_three_names)
        assert len(results) == 3
        
        # Test NOT IN
        results = await crud_ins.select_models(db_session, name__not_in=['nonexistent'])
        assert len(results) == len(populated_db)
        
        # Test BETWEEN
        results = await crud_ins.select_models(db_session, id__between=[1, 1000])
        assert len(results) >= len(populated_db)

    @pytest.mark.asyncio
    async def test_select_with_null_filters(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test selecting with NULL-related filters."""
        # Test IS NULL
        results = await crud_ins.select_models(db_session, name__is=None)
        assert isinstance(results, list)
        
        # Test IS NOT NULL
        results = await crud_ins.select_models(db_session, name__is_not=None)
        assert len(results) == len(populated_db)


class TestSelectWithSorting:
    """Test selecting models with sorting."""

    @pytest.mark.asyncio
    async def test_select_with_single_column_sort(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test selecting with single column sorting."""
        # Test ascending sort
        results = await crud_ins.select_models_order(
            db_session,
            sort_columns='name',
            sort_orders='asc'
        )
        
        names = [r.name for r in results]
        assert names == sorted(names)
        
        # Test descending sort
        results = await crud_ins.select_models_order(
            db_session,
            sort_columns='name',
            sort_orders='desc'
        )
        
        names = [r.name for r in results]
        assert names == sorted(names, reverse=True)

    @pytest.mark.asyncio
    async def test_select_with_multiple_column_sort(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test selecting with multiple column sorting."""
        results = await crud_ins.select_models_order(
            db_session,
            sort_columns=['del_flag', 'name'],
            sort_orders=['asc', 'desc']
        )
        
        assert len(results) == len(populated_db)
        # Verify sorting logic: first by del_flag ASC, then by name DESC

    @pytest.mark.asyncio
    async def test_select_with_default_sort_order(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test selecting with default sort order (ascending)."""
        results = await crud_ins.select_models_order(
            db_session,
            sort_columns='name'
        )
        
        names = [r.name for r in results]
        assert names == sorted(names)

    @pytest.mark.asyncio
    async def test_select_sort_with_filters(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test selecting with both sorting and filters."""
        results = await crud_ins.select_models_order(
            db_session,
            sort_columns='name',
            sort_orders='asc',
            del_flag=False
        )
        
        # Verify all results match filter
        for result in results:
            assert result.del_flag is False
        
        # Verify sorting
        names = [r.name for r in results]
        assert names == sorted(names)


class TestSelectComplexFilters:
    """Test selecting with complex filter combinations."""

    @pytest.mark.asyncio
    async def test_select_with_or_filters(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test selecting with OR filters."""
        results = await crud_ins.select_models(
            db_session,
            name__or={'eq': 'item_1', 'like': 'item_2%'}
        )
        assert len(results) >= 1

    @pytest.mark.asyncio
    async def test_select_with_arithmetic_filters(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test selecting with arithmetic filters."""
        results = await crud_ins.select_models(
            db_session,
            id__add={'value': 1, 'condition': {'gt': 1}}
        )
        assert isinstance(results, list)

    @pytest.mark.asyncio
    async def test_select_with_multiple_filters(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test selecting with multiple filter conditions."""
        results = await crud_ins.select_models(
            db_session,
            name__startswith='item_',
            del_flag=False,
            id__gt=0
        )
        
        for result in results:
            assert result.name.startswith('item_')
            assert result.del_flag is False
            assert result.id > 0


class TestCountOperations:
    """Test count operations."""

    @pytest.mark.asyncio
    async def test_count_all(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test counting all records."""
        count = await crud_ins.count(db_session)
        assert count == len(populated_db)

    @pytest.mark.asyncio
    async def test_count_with_filters(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test counting with filters."""
        count = await crud_ins.count(db_session, del_flag=False)
        assert count > 0
        
        count = await crud_ins.count(db_session, name__startswith='item_')
        assert count == len(populated_db)

    @pytest.mark.asyncio
    async def test_count_with_nonexistent_filter(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test counting with non-existent filter."""
        count = await crud_ins.count(db_session, name='nonexistent')
        assert count == 0

    @pytest.mark.asyncio
    async def test_count_with_complex_filters(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test counting with complex filters."""
        count = await crud_ins.count(
            db_session,
            name__startswith='item_',
            del_flag=False
        )
        assert count >= 0


class TestExistsOperations:
    """Test exists operations."""

    @pytest.mark.asyncio
    async def test_exists_with_data(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test exists operation when data exists."""
        exists = await crud_ins.exists(db_session, name='item_1')
        assert exists is True

    @pytest.mark.asyncio
    async def test_exists_without_data(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test exists operation when data doesn't exist."""
        exists = await crud_ins.exists(db_session, name='nonexistent')
        assert exists is False

    @pytest.mark.asyncio
    async def test_exists_with_filters(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test exists operation with complex filters."""
        exists = await crud_ins.exists(
            db_session,
            name__startswith='item_',
            del_flag=False
        )
        assert exists is True

    @pytest.mark.asyncio
    async def test_exists_without_filters(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test exists operation without any filters."""
        exists = await crud_ins.exists(db_session)
        assert exists is True


class TestSelectEdgeCases:
    """Test edge cases in select operations."""

    @pytest.mark.asyncio
    async def test_select_with_empty_string_filter(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test selecting with empty string filter."""
        # First create a record with empty name
        async with db_session.begin():
            data = ModelTest(name='')
            await crud_ins.create_model(db_session, data)
        
        results = await crud_ins.select_models(db_session, name='')
        assert len(results) >= 1
        assert results[0].name == ''

    @pytest.mark.asyncio
    async def test_select_with_whitespace_filter(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test selecting with whitespace filter."""
        # First create a record with whitespace name
        async with db_session.begin():
            data = ModelTest(name='   ')
            await crud_ins.create_model(db_session, data)
        
        results = await crud_ins.select_models(db_session, name='   ')
        assert len(results) >= 1
        assert results[0].name == '   '

    @pytest.mark.asyncio
    async def test_select_with_special_characters(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test selecting with special characters."""
        special_names = ["test'quote", 'test"double', 'test\\backslash']
        
        # Create records with special characters
        async with db_session.begin():
            for name in special_names:
                data = ModelTest(name=name)
                await crud_ins.create_model(db_session, data)
        
        # Test retrieval
        for name in special_names:
            results = await crud_ins.select_models(db_session, name=name)
            assert len(results) >= 1
            assert results[0].name == name
