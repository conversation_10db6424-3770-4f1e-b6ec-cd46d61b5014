#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Unit tests for utility functions in SQLAlchemy CRUD Plus.
"""
import pytest
from sqlalchemy import select
from sqlalchemy.orm import aliased

from sqlalchemy_crud_plus.utils import (
    get_column,
    get_sqlalchemy_filter,
    parse_filters,
    apply_sorting,
    _create_or_filters,
    _create_arithmetic_filters,
    _create_and_filters,
)
from sqlalchemy_crud_plus.errors import (
    ModelColumnError,
    SelectOperatorError,
    ColumnSortError,
)
from tests.model import Ins


class TestGetColumn:
    """Test get_column utility function."""

    def test_get_valid_column(self):
        """Test getting valid columns from model."""
        name_column = get_column(Ins, 'name')
        assert name_column is not None
        assert hasattr(name_column, 'property')
        
        id_column = get_column(Ins, 'id')
        assert id_column is not None
        
        del_flag_column = get_column(Ins, 'del_flag')
        assert del_flag_column is not None

    def test_get_invalid_column(self):
        """Test getting invalid columns from model."""
        with pytest.raises(ModelColumnError):
            get_column(Ins, 'nonexistent_column')
        
        with pytest.raises(ModelColumnError):
            get_column(Ins, '')

    def test_get_column_with_aliased_model(self):
        """Test getting columns from aliased model."""
        aliased_ins = aliased(Ins)
        
        name_column = get_column(aliased_ins, 'name')
        assert name_column is not None
        
        with pytest.raises(ModelColumnError):
            get_column(aliased_ins, 'nonexistent_column')

    def test_get_column_validation(self):
        """Test column validation logic."""
        # Test with valid column that has property.columns
        column = get_column(Ins, 'name')
        assert hasattr(column, 'property')
        
        # Test column type validation
        id_column = get_column(Ins, 'id')
        assert hasattr(id_column, 'property')


class TestGetSQLAlchemyFilter:
    """Test get_sqlalchemy_filter utility function."""

    def test_basic_operators(self):
        """Test basic SQLAlchemy filter operators."""
        # Test equality
        filter_func = get_sqlalchemy_filter('eq', 'test')
        assert filter_func is not None
        
        # Test inequality
        filter_func = get_sqlalchemy_filter('ne', 'test')
        assert filter_func is not None
        
        # Test greater than
        filter_func = get_sqlalchemy_filter('gt', 5)
        assert filter_func is not None
        
        # Test less than
        filter_func = get_sqlalchemy_filter('lt', 5)
        assert filter_func is not None

    def test_string_operators(self):
        """Test string-specific operators."""
        # Test LIKE
        filter_func = get_sqlalchemy_filter('like', '%test%')
        assert filter_func is not None
        
        # Test ILIKE
        filter_func = get_sqlalchemy_filter('ilike', '%TEST%')
        assert filter_func is not None
        
        # Test startswith
        filter_func = get_sqlalchemy_filter('startswith', 'test')
        assert filter_func is not None
        
        # Test endswith
        filter_func = get_sqlalchemy_filter('endswith', 'test')
        assert filter_func is not None
        
        # Test contains
        filter_func = get_sqlalchemy_filter('contains', 'test')
        assert filter_func is not None

    def test_list_operators(self):
        """Test operators that work with lists."""
        # Test IN
        filter_func = get_sqlalchemy_filter('in', [1, 2, 3])
        assert filter_func is not None
        
        # Test NOT IN
        filter_func = get_sqlalchemy_filter('not_in', [1, 2, 3])
        assert filter_func is not None
        
        # Test BETWEEN
        filter_func = get_sqlalchemy_filter('between', [1, 10])
        assert filter_func is not None

    def test_null_operators(self):
        """Test NULL-related operators."""
        # Test IS NULL
        filter_func = get_sqlalchemy_filter('is', None)
        assert filter_func is not None
        
        # Test IS NOT NULL
        filter_func = get_sqlalchemy_filter('is_not', None)
        assert filter_func is not None

    def test_arithmetic_operators(self):
        """Test arithmetic operators."""
        # Test addition
        filter_func = get_sqlalchemy_filter('add', 5)
        assert filter_func is not None
        
        # Test subtraction
        filter_func = get_sqlalchemy_filter('sub', 5)
        assert filter_func is not None
        
        # Test multiplication
        filter_func = get_sqlalchemy_filter('mul', 2)
        assert filter_func is not None
        
        # Test division
        filter_func = get_sqlalchemy_filter('truediv', 2)
        assert filter_func is not None

    def test_unsupported_operator(self):
        """Test unsupported operators."""
        filter_func = get_sqlalchemy_filter('unsupported_op', 'value')
        assert filter_func is None

    def test_arithmetic_disabled(self):
        """Test arithmetic operators when disabled."""
        from sqlalchemy_crud_plus.errors import SelectOperatorError

        with pytest.raises(SelectOperatorError):
            get_sqlalchemy_filter('add', 5, allow_arithmetic=False)


class TestParseFilters:
    """Test parse_filters utility function."""

    def test_parse_simple_filters(self):
        """Test parsing simple filters."""
        filters = parse_filters(Ins, name='test', id=1)
        assert len(filters) == 2
        
        filters = parse_filters(Ins, name__eq='test')
        assert len(filters) == 1
        
        filters = parse_filters(Ins, id__gt=5)
        assert len(filters) == 1

    def test_parse_complex_filters(self):
        """Test parsing complex filters."""
        filters = parse_filters(
            Ins,
            name__like='%test%',
            id__between=[1, 10],
            del_flag=True
        )
        assert len(filters) == 3

    def test_parse_or_conditions(self):
        """Test parsing OR conditions."""
        # Test simple OR
        filters = parse_filters(
            Ins,
            name__or={'eq': 'test1', 'like': '%test2%'}
        )
        assert len(filters) == 1
        
        # Test complex OR group
        filters = parse_filters(
            Ins,
            __or__=[
                {'name__eq': 'test1'},
                {'id__gt': 5}
            ]
        )
        assert len(filters) == 1

    def test_parse_arithmetic_filters(self):
        """Test parsing arithmetic filters."""
        filters = parse_filters(
            Ins,
            id__add={'value': 5, 'condition': {'gt': 10}}
        )
        assert len(filters) == 1
        
        filters = parse_filters(
            Ins,
            id__mul={'value': 2, 'condition': {'lt': 20}}
        )
        assert len(filters) == 1

    def test_parse_empty_filters(self):
        """Test parsing empty filters."""
        filters = parse_filters(Ins)
        assert len(filters) == 0
        
        filters = parse_filters(Ins, **{})
        assert len(filters) == 0

    def test_parse_invalid_column(self):
        """Test parsing with invalid column."""
        with pytest.raises(ModelColumnError):
            parse_filters(Ins, nonexistent_column='value')

    def test_parse_mixed_conditions(self):
        """Test parsing mixed filter conditions."""
        filters = parse_filters(
            Ins,
            name__startswith='test',
            id__between=[1, 100],
            del_flag=True  # Simple condition instead of complex OR
        )
        assert len(filters) == 3


class TestApplySorting:
    """Test apply_sorting utility function."""

    def test_single_column_sort(self):
        """Test applying sorting to single column."""
        stmt = select(Ins)
        
        # Test ascending sort
        sorted_stmt = apply_sorting(Ins, stmt, 'name', 'asc')
        assert sorted_stmt is not None
        
        # Test descending sort
        sorted_stmt = apply_sorting(Ins, stmt, 'name', 'desc')
        assert sorted_stmt is not None
        
        # Test default sort (should be ascending)
        sorted_stmt = apply_sorting(Ins, stmt, 'name')
        assert sorted_stmt is not None

    def test_multiple_column_sort(self):
        """Test applying sorting to multiple columns."""
        stmt = select(Ins)
        
        # Test multiple columns with multiple orders
        sorted_stmt = apply_sorting(
            Ins, stmt, 
            ['name', 'id'], 
            ['asc', 'desc']
        )
        assert sorted_stmt is not None
        
        # Test multiple columns with single order
        sorted_stmt = apply_sorting(
            Ins, stmt, 
            ['name', 'id'], 
            'asc'
        )
        assert sorted_stmt is not None

    def test_sort_validation_errors(self):
        """Test sorting validation errors."""
        stmt = select(Ins)
        
        # Test mismatched columns and orders
        with pytest.raises(ColumnSortError):
            apply_sorting(
                Ins, stmt,
                ['name', 'id'],
                ['asc']  # Only one order for two columns
            )
        
        # Test invalid sort order
        with pytest.raises(SelectOperatorError):
            apply_sorting(Ins, stmt, 'name', 'invalid_order')
        
        # Test invalid column
        with pytest.raises(ModelColumnError):
            apply_sorting(Ins, stmt, 'nonexistent_column')
        
        # Test providing orders without columns
        with pytest.raises(ValueError):
            apply_sorting(Ins, stmt, None, ['asc'])

    def test_sort_edge_cases(self):
        """Test sorting edge cases."""
        stmt = select(Ins)
        
        # Test with None columns (should return original statement)
        result_stmt = apply_sorting(Ins, stmt, None)
        assert result_stmt is stmt
        
        # Test with empty string columns
        result_stmt = apply_sorting(Ins, stmt, '')
        assert result_stmt is stmt
        
        # Test with empty list
        result_stmt = apply_sorting(Ins, stmt, [])
        assert result_stmt is stmt


class TestHelperFunctions:
    """Test helper functions for filter creation."""

    def test_create_or_filters(self):
        """Test OR filter creation helper."""
        column = get_column(Ins, 'name')
        
        or_conditions = {
            'eq': 'test1',
            'like': '%test2%'
        }
        
        or_filters = _create_or_filters(column, 'or', or_conditions)
        assert len(or_filters) == 2

    def test_create_arithmetic_filters(self):
        """Test arithmetic filter creation helper."""
        column = get_column(Ins, 'id')
        
        # Test valid arithmetic condition
        arith_filter = _create_arithmetic_filters(
            column, 'add',
            {'value': 5, 'condition': {'gt': 10}}
        )
        assert len(arith_filter) == 1
        
        # Test invalid arithmetic condition
        arith_filter = _create_arithmetic_filters(
            column, 'add',
            {'invalid': 'structure'}
        )
        assert len(arith_filter) == 0

    def test_create_and_filters(self):
        """Test AND filter creation helper."""
        column = get_column(Ins, 'name')
        
        and_filters = _create_and_filters(column, 'eq', 'test')
        assert len(and_filters) == 1
        
        # Test with BETWEEN operator
        id_column = get_column(Ins, 'id')
        and_filters = _create_and_filters(id_column, 'between', [1, 10])
        assert len(and_filters) == 1

    def test_create_filters_with_none_values(self):
        """Test filter creation with None values."""
        column = get_column(Ins, 'name')
        
        # Test with None value
        and_filters = _create_and_filters(column, 'eq', None)
        assert len(and_filters) == 1
        
        # Test IS NULL
        and_filters = _create_and_filters(column, 'is', None)
        assert len(and_filters) == 1


class TestFilterEdgeCases:
    """Test edge cases in filter processing."""

    def test_filter_with_empty_values(self):
        """Test filters with empty values."""
        column = get_column(Ins, 'name')
        
        # Test with empty string
        filter_func = get_sqlalchemy_filter('eq', '')
        assert filter_func is not None
        
        # Test with zero value
        id_column = get_column(Ins, 'id')
        filter_func = get_sqlalchemy_filter('eq', 0)
        assert filter_func is not None

    def test_filter_with_boolean_values(self):
        """Test filters with boolean values."""
        bool_column = get_column(Ins, 'del_flag')
        
        filter_func = get_sqlalchemy_filter('eq', True)
        assert filter_func is not None
        
        filter_func = get_sqlalchemy_filter('eq', False)
        assert filter_func is not None

    def test_filter_with_special_characters(self):
        """Test filters with special characters."""
        column = get_column(Ins, 'name')
        
        special_values = ["test'quote", 'test"double', 'test\\backslash', 'test%percent']
        
        for value in special_values:
            filter_func = get_sqlalchemy_filter('eq', value)
            assert filter_func is not None

    def test_complex_nested_conditions(self):
        """Test complex nested filter conditions."""
        # Test nested OR with AND conditions
        filters = parse_filters(
            Ins,
            __or__=[
                {'name__eq': 'test1', 'id__gt': 5},
                {'name__like': '%test2%', 'del_flag': True}
            ]
        )
        assert len(filters) == 1
        
        # Test mixed conditions
        filters = parse_filters(
            Ins,
            name__startswith='test',
            id__between=[1, 100],
            __or__=[
                {'del_flag': True},
                {'name__endswith': '_special'}
            ]
        )
        assert len(filters) == 3

    def test_arithmetic_filter_edge_cases(self):
        """Test arithmetic filter edge cases."""
        column = get_column(Ins, 'id')
        
        # Test with missing 'value' key
        arith_filter = _create_arithmetic_filters(
            column, 'add',
            {'condition': {'gt': 10}}
        )
        assert len(arith_filter) == 0
        
        # Test with missing 'condition' key
        arith_filter = _create_arithmetic_filters(
            column, 'add',
            {'value': 5}
        )
        assert len(arith_filter) == 0
        
        # Test with invalid condition operator
        arith_filter = _create_arithmetic_filters(
            column, 'add',
            {'value': 5, 'condition': {'invalid_op': 10}}
        )
        assert len(arith_filter) == 0

    def test_or_filter_edge_cases(self):
        """Test OR filter edge cases."""
        column = get_column(Ins, 'name')
        
        # Test with empty OR conditions
        or_filters = _create_or_filters(column, 'or', {})
        assert len(or_filters) == 0
        
        # Test with invalid OR operator
        or_filters = _create_or_filters(column, 'or', {'invalid_op': 'value'})
        assert len(or_filters) == 0
