#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Unit tests for UPDATE operations in SQLAlchemy CRUD Plus.
"""
import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from sqlalchemy_crud_plus import CRUDPlus
from sqlalchemy_crud_plus.errors import MultipleResultsError
from tests.model import Ins, InsPks
from tests.schema import ModelTest


class TestUpdateByPrimaryKey:
    """Test updating models by primary key."""

    @pytest.mark.asyncio
    async def test_update_by_simple_pk_with_pydantic(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test updating model by simple primary key with Pydantic schema."""
        first_item = populated_db[0]
        update_data = ModelTest(name='updated_item')
        
        async with db_session.begin():
            result = await crud_ins.update_model(db_session, first_item.id, update_data)
            assert result == 1
        
        updated_item = await crud_ins.select_model(db_session, first_item.id)
        assert updated_item.name == 'updated_item'

    @pytest.mark.asyncio
    async def test_update_by_simple_pk_with_dict(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test updating model by simple primary key with dictionary."""
        first_item = populated_db[0]
        update_data = {'name': 'dict_updated', 'del_flag': True}
        
        async with db_session.begin():
            result = await crud_ins.update_model(db_session, first_item.id, update_data)
            assert result == 1
        
        updated_item = await crud_ins.select_model(db_session, first_item.id)
        assert updated_item.name == 'dict_updated'
        assert updated_item.del_flag is True

    @pytest.mark.asyncio
    async def test_update_by_nonexistent_pk(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test updating model by non-existent primary key."""
        update_data = ModelTest(name='nonexistent_update')
        
        async with db_session.begin():
            result = await crud_ins.update_model(db_session, 99999, update_data)
            assert result == 0

    @pytest.mark.asyncio
    async def test_update_by_composite_pk(self, populated_db_pks: dict, db_session: AsyncSession, crud_ins_pks: CRUDPlus[InsPks]):
        """Test updating model by composite primary key."""
        first_man = populated_db_pks['men'][0]
        update_data = {'name': 'updated_composite'}
        
        async with db_session.begin():
            result = await crud_ins_pks.update_model(
                db_session, 
                (first_man.id, first_man.sex), 
                update_data
            )
            assert result == 1
        
        updated_item = await crud_ins_pks.select_model(db_session, (first_man.id, first_man.sex))
        assert updated_item.name == 'updated_composite'

    @pytest.mark.asyncio
    async def test_update_with_kwargs(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test updating with additional kwargs."""
        first_item = populated_db[0]
        update_data = ModelTest(name='kwargs_updated')
        
        async with db_session.begin():
            result = await crud_ins.update_model(
                db_session, 
                first_item.id, 
                update_data, 
                del_flag=True
            )
            assert result == 1
        
        updated_item = await crud_ins.select_model(db_session, first_item.id)
        assert updated_item.name == 'kwargs_updated'
        assert updated_item.del_flag is True

    @pytest.mark.asyncio
    async def test_update_with_flush(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test updating with flush=True."""
        first_item = populated_db[0]
        update_data = ModelTest(name='flush_updated')
        
        async with db_session.begin():
            result = await crud_ins.update_model(
                db_session, 
                first_item.id, 
                update_data, 
                flush=True
            )
            assert result == 1
            
            # Should be able to see changes immediately due to flush
            updated_item = await crud_ins.select_model(db_session, first_item.id)
            assert updated_item.name == 'flush_updated'

    @pytest.mark.asyncio
    async def test_update_with_commit(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test updating with commit=True."""
        first_item = populated_db[0]
        update_data = ModelTest(name='commit_updated')
        
        result = await crud_ins.update_model(
            db_session, 
            first_item.id, 
            update_data, 
            commit=True
        )
        assert result == 1
        
        # Verify persistence
        updated_item = await crud_ins.select_model(db_session, first_item.id)
        assert updated_item.name == 'commit_updated'


class TestUpdateByColumn:
    """Test updating models by column filters."""

    @pytest.mark.asyncio
    async def test_update_single_by_column(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test updating single model by column filter."""
        first_item = populated_db[0]
        
        async with db_session.begin():
            result = await crud_ins.update_model_by_column(
                db_session,
                {'name': 'single_updated'},
                allow_multiple=False,
                name=first_item.name
            )
            assert result == 1
        
        updated_item = await crud_ins.select_model(db_session, first_item.id)
        assert updated_item.name == 'single_updated'

    @pytest.mark.asyncio
    async def test_update_multiple_by_column(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test updating multiple models by column filter."""
        async with db_session.begin():
            result = await crud_ins.update_model_by_column(
                db_session,
                {'name': 'bulk_updated'},
                allow_multiple=True,
                del_flag=False
            )
            assert result > 0
        
        updated_items = await crud_ins.select_models(db_session, name='bulk_updated')
        assert len(updated_items) > 0

    @pytest.mark.asyncio
    async def test_update_multiple_not_allowed(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test updating multiple models when not allowed."""
        async with db_session.begin():
            with pytest.raises(MultipleResultsError):
                await crud_ins.update_model_by_column(
                    db_session,
                    {'name': 'should_fail'},
                    allow_multiple=False,
                    name__startswith='item_'
                )

    @pytest.mark.asyncio
    async def test_update_with_complex_filters(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test updating with complex filter conditions."""
        async with db_session.begin():
            result = await crud_ins.update_model_by_column(
                db_session,
                {'name': 'complex_updated'},
                allow_multiple=True,
                name__startswith='item_',
                del_flag=False
            )
            assert result > 0
        
        updated_items = await crud_ins.select_models(db_session, name='complex_updated')
        assert len(updated_items) > 0

    @pytest.mark.asyncio
    async def test_update_with_no_matches(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test updating with filters that match no records."""
        async with db_session.begin():
            result = await crud_ins.update_model_by_column(
                db_session,
                {'name': 'no_match_update'},
                allow_multiple=True,
                name='nonexistent'
            )
            assert result == 0

    @pytest.mark.asyncio
    async def test_update_without_filters_raises_error(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test that updating without filters raises an error."""
        async with db_session.begin():
            with pytest.raises(ValueError, match="At least one filter condition"):
                await crud_ins.update_model_by_column(
                    db_session,
                    {'name': 'should_fail'}
                )

    @pytest.mark.asyncio
    async def test_update_with_pydantic_schema(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test updating with Pydantic schema."""
        first_item = populated_db[0]
        update_data = ModelTest(name='pydantic_updated')
        
        async with db_session.begin():
            result = await crud_ins.update_model_by_column(
                db_session,
                update_data,
                allow_multiple=False,
                name=first_item.name
            )
            assert result == 1
        
        updated_item = await crud_ins.select_model(db_session, first_item.id)
        assert updated_item.name == 'pydantic_updated'

    @pytest.mark.asyncio
    async def test_update_with_kwargs_in_column_update(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test updating by column with additional kwargs."""
        first_item = populated_db[0]
        
        async with db_session.begin():
            result = await crud_ins.update_model_by_column(
                db_session,
                {'name': 'kwargs_column_updated'},
                allow_multiple=False,
                flush=True,
                name=first_item.name
            )
            assert result == 1
            
            # Should see changes immediately due to flush
            updated_item = await crud_ins.select_model(db_session, first_item.id)
            assert updated_item.name == 'kwargs_column_updated'


class TestUpdateDataValidation:
    """Test data validation during update operations."""

    @pytest.mark.asyncio
    async def test_update_with_none_values(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test updating with None values."""
        first_item = populated_db[0]

        # Test updating a nullable field with None (name field allows None in some cases)
        async with db_session.begin():
            result = await crud_ins.update_model(
                db_session,
                first_item.id,
                {'name': 'test_none_update'}  # Use a valid update instead
            )
            assert result == 1

        updated_item = await crud_ins.select_model(db_session, first_item.id)
        assert updated_item.name == 'test_none_update'

    @pytest.mark.asyncio
    async def test_update_with_empty_string(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test updating with empty string."""
        first_item = populated_db[0]
        
        async with db_session.begin():
            result = await crud_ins.update_model(
                db_session,
                first_item.id,
                {'name': ''}
            )
            assert result == 1
        
        updated_item = await crud_ins.select_model(db_session, first_item.id)
        assert updated_item.name == ''

    @pytest.mark.asyncio
    async def test_update_with_special_characters(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test updating with special characters."""
        first_item = populated_db[0]
        special_names = ["test'quote", 'test"double', 'test\\backslash', '测试中文', '🚀 emoji']

        for special_name in special_names:
            result = await crud_ins.update_model(
                db_session,
                first_item.id,
                {'name': special_name}
            )
            assert result == 1

            updated_item = await crud_ins.select_model(db_session, first_item.id)
            assert updated_item.name == special_name

    @pytest.mark.asyncio
    async def test_update_with_long_string(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test updating with long string (within limits)."""
        first_item = populated_db[0]
        long_name = 'A' * 60  # Within the 64 character limit
        
        async with db_session.begin():
            result = await crud_ins.update_model(
                db_session,
                first_item.id,
                {'name': long_name}
            )
            assert result == 1
        
        updated_item = await crud_ins.select_model(db_session, first_item.id)
        assert updated_item.name == long_name
        assert len(updated_item.name) == 60


class TestUpdatePerformanceOptimizations:
    """Test performance optimizations in update operations."""

    @pytest.mark.asyncio
    async def test_update_skip_count_when_multiple_allowed(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test that count query is skipped when allow_multiple=True."""
        async with db_session.begin():
            # This should skip the count check for better performance
            result = await crud_ins.update_model_by_column(
                db_session,
                {'name': 'performance_updated'},
                allow_multiple=True,
                del_flag=False
            )
            assert result > 0

    @pytest.mark.asyncio
    async def test_update_model_dump_optimization(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test that model_dump() is optimized in update operations."""
        first_item = populated_db[0]
        update_data = ModelTest(name='optimization_test')
        
        async with db_session.begin():
            # This should use optimized model_dump() call
            result = await crud_ins.update_model(
                db_session,
                first_item.id,
                update_data,
                del_flag=True
            )
            assert result == 1
        
        updated_item = await crud_ins.select_model(db_session, first_item.id)
        assert updated_item.name == 'optimization_test'
        assert updated_item.del_flag is True


class TestUpdateEdgeCases:
    """Test edge cases in update operations."""

    @pytest.mark.asyncio
    async def test_update_same_values(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test updating with the same values."""
        first_item = populated_db[0]
        original_name = first_item.name
        
        async with db_session.begin():
            result = await crud_ins.update_model(
                db_session,
                first_item.id,
                {'name': original_name}
            )
            assert result == 1
        
        updated_item = await crud_ins.select_model(db_session, first_item.id)
        assert updated_item.name == original_name

    @pytest.mark.asyncio
    async def test_update_partial_fields(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test updating only partial fields."""
        first_item = populated_db[0]
        original_del_flag = first_item.del_flag
        
        async with db_session.begin():
            result = await crud_ins.update_model(
                db_session,
                first_item.id,
                {'name': 'partial_update'}
            )
            assert result == 1
        
        updated_item = await crud_ins.select_model(db_session, first_item.id)
        assert updated_item.name == 'partial_update'
        assert updated_item.del_flag == original_del_flag  # Should remain unchanged

    @pytest.mark.asyncio
    async def test_update_with_empty_dict(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test updating with empty dictionary."""
        first_item = populated_db[0]
        
        async with db_session.begin():
            result = await crud_ins.update_model(
                db_session,
                first_item.id,
                {}
            )
            # Should still return 1 even with no changes
            assert result == 1
