#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Unit tests for error handling in SQLAlchemy CRUD Plus.
"""
import pytest
from sqlalchemy import <PERSON>umn, Integer, String
from sqlalchemy.orm import DeclarativeBase
from sqlalchemy.ext.asyncio import AsyncSession

from sqlalchemy_crud_plus import CRUDPlus
from sqlalchemy_crud_plus.errors import (
    CompositePrimaryKeysError,
    ModelColumnError,
    MultipleResultsError,
    SelectOperatorError,
    ColumnSortError,
)
from tests.model import Ins, InsPks
from tests.schema import ModelTest


class ErrorTestBase(DeclarativeBase):
    """Base class for error test models."""
    pass


class ModelWithoutDelFlag(ErrorTestBase):
    """Test model without del_flag column."""
    __tablename__ = 'model_without_del_flag'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(50))


class TestCompositePrimaryKeyErrors:
    """Test composite primary key error handling."""

    @pytest.mark.asyncio
    async def test_select_with_wrong_key_count(self, db_session: AsyncSession, crud_ins_pks: CRUDPlus[InsPks]):
        """Test selecting with wrong number of key components."""
        # Test with too few key components
        with pytest.raises(CompositePrimaryKeysError):
            await crud_ins_pks.select_model(db_session, (1,))
        
        # Test with too many key components
        with pytest.raises(CompositePrimaryKeysError):
            await crud_ins_pks.select_model(db_session, (1, 'men', 'extra'))

    @pytest.mark.asyncio
    async def test_update_with_wrong_key_count(self, db_session: AsyncSession, crud_ins_pks: CRUDPlus[InsPks]):
        """Test updating with wrong number of key components."""
        update_data = {'name': 'test_update'}
        
        # Test with too few key components
        with pytest.raises(CompositePrimaryKeysError):
            await crud_ins_pks.update_model(db_session, (1,), update_data)
        
        # Test with too many key components
        with pytest.raises(CompositePrimaryKeysError):
            await crud_ins_pks.update_model(db_session, (1, 'men', 'extra'), update_data)

    @pytest.mark.asyncio
    async def test_delete_with_wrong_key_count(self, db_session: AsyncSession, crud_ins_pks: CRUDPlus[InsPks]):
        """Test deleting with wrong number of key components."""
        # Test with too few key components
        with pytest.raises(CompositePrimaryKeysError):
            await crud_ins_pks.delete_model(db_session, (1,))
        
        # Test with too many key components
        with pytest.raises(CompositePrimaryKeysError):
            await crud_ins_pks.delete_model(db_session, (1, 'men', 'extra'))


class TestModelColumnErrors:
    """Test model column validation errors."""

    @pytest.mark.asyncio
    async def test_select_with_invalid_column(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test selecting with non-existent column in filter."""
        with pytest.raises(ModelColumnError):
            await crud_ins.select_models(db_session, nonexistent_column='value')

    @pytest.mark.asyncio
    async def test_update_with_invalid_column_filter(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test updating with non-existent column in filter."""
        with pytest.raises(ModelColumnError):
            await crud_ins.update_model_by_column(
                db_session,
                {'name': 'test'},
                nonexistent_column='value'
            )

    @pytest.mark.asyncio
    async def test_delete_with_invalid_column_filter(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test deleting with non-existent column in filter."""
        with pytest.raises(ModelColumnError):
            await crud_ins.delete_model_by_column(
                db_session,
                nonexistent_column='value'
            )

    @pytest.mark.asyncio
    async def test_sort_with_invalid_column(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test sorting with non-existent column."""
        with pytest.raises(ModelColumnError):
            await crud_ins.select_models_order(
                db_session,
                sort_columns='nonexistent_column'
            )

    @pytest.mark.asyncio
    async def test_logical_deletion_invalid_column(self, db_session: AsyncSession):
        """Test logical deletion with non-existent column."""
        crud_without_del_flag = CRUDPlus(ModelWithoutDelFlag)
        
        with pytest.raises(ModelColumnError):
            await crud_without_del_flag.delete_model_by_column(
                db_session,
                logical_deletion=True,
                deleted_flag_column='del_flag',
                name='test'
            )


class TestMultipleResultsErrors:
    """Test multiple results error handling."""

    @pytest.mark.asyncio
    async def test_update_multiple_not_allowed(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test updating multiple records when not allowed."""
        async with db_session.begin():
            with pytest.raises(MultipleResultsError):
                await crud_ins.update_model_by_column(
                    db_session,
                    {'name': 'should_fail'},
                    allow_multiple=False,
                    name__startswith='item_'
                )

    @pytest.mark.asyncio
    async def test_delete_multiple_not_allowed(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test deleting multiple records when not allowed."""
        async with db_session.begin():
            with pytest.raises(MultipleResultsError):
                await crud_ins.delete_model_by_column(
                    db_session,
                    allow_multiple=False,
                    name__startswith='item_'
                )

    @pytest.mark.asyncio
    async def test_update_single_allowed_with_multiple_matches(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test that single update is allowed when only one record matches."""
        first_item = populated_db[0]
        
        async with db_session.begin():
            # This should work because only one record matches
            result = await crud_ins.update_model_by_column(
                db_session,
                {'name': 'single_update_success'},
                allow_multiple=False,
                name=first_item.name
            )
            assert result == 1

    @pytest.mark.asyncio
    async def test_delete_single_allowed_with_multiple_matches(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test that single delete is allowed when only one record matches."""
        first_item = populated_db[0]
        
        async with db_session.begin():
            # This should work because only one record matches
            result = await crud_ins.delete_model_by_column(
                db_session,
                allow_multiple=False,
                name=first_item.name
            )
            assert result == 1


class TestSelectOperatorErrors:
    """Test select operator validation errors."""

    @pytest.mark.asyncio
    async def test_invalid_sort_order(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test invalid sort order."""
        with pytest.raises(SelectOperatorError):
            await crud_ins.select_models_order(
                db_session,
                sort_columns='name',
                sort_orders='invalid_order'
            )

    @pytest.mark.asyncio
    async def test_invalid_operator_in_filter(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test invalid operator in filter."""
        # This should not raise an error but return empty results
        results = await crud_ins.select_models(db_session, name__invalid_op='test')
        assert isinstance(results, list)

    def test_invalid_list_operator_input(self):
        """Test invalid input for list operators."""
        from sqlalchemy_crud_plus.utils import get_sqlalchemy_filter
        from sqlalchemy_crud_plus.errors import SelectOperatorError

        # Test IN operator with non-list input should raise error
        with pytest.raises(SelectOperatorError):
            get_sqlalchemy_filter('in', 'not_a_list')


class TestColumnSortErrors:
    """Test column sorting validation errors."""

    @pytest.mark.asyncio
    async def test_mismatched_columns_and_orders(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test mismatched columns and orders count."""
        with pytest.raises(ColumnSortError):
            await crud_ins.select_models_order(
                db_session,
                sort_columns=['name', 'id'],
                sort_orders=['asc']  # Only one order for two columns
            )

    @pytest.mark.asyncio
    async def test_too_many_orders_for_columns(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test too many sort orders for columns."""
        with pytest.raises(ColumnSortError):
            await crud_ins.select_models_order(
                db_session,
                sort_columns=['name'],
                sort_orders=['asc', 'desc']  # Two orders for one column
            )

    def test_sort_orders_without_columns(self):
        """Test providing sort orders without columns."""
        from sqlalchemy_crud_plus.utils import apply_sorting
        from sqlalchemy import select
        
        with pytest.raises(ValueError):
            apply_sorting(Ins, select(Ins), None, ['asc'])


class TestValidationErrors:
    """Test input validation errors."""

    @pytest.mark.asyncio
    async def test_update_without_filters(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test updating without any filter conditions."""
        async with db_session.begin():
            with pytest.raises(ValueError, match="At least one filter condition"):
                await crud_ins.update_model_by_column(
                    db_session,
                    {'name': 'test'}
                )

    @pytest.mark.asyncio
    async def test_delete_without_filters(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test deleting without any filter conditions."""
        async with db_session.begin():
            with pytest.raises(ValueError, match="At least one filter condition"):
                await crud_ins.delete_model_by_column(db_session)

    @pytest.mark.asyncio
    async def test_update_with_empty_kwargs(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test updating with empty kwargs (no filters)."""
        async with db_session.begin():
            with pytest.raises(ValueError, match="At least one filter condition"):
                await crud_ins.update_model_by_column(
                    db_session,
                    {'name': 'test'},
                    **{}
                )

    @pytest.mark.asyncio
    async def test_delete_with_empty_kwargs(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test deleting with empty kwargs (no filters)."""
        async with db_session.begin():
            with pytest.raises(ValueError, match="At least one filter condition"):
                await crud_ins.delete_model_by_column(
                    db_session,
                    **{}
                )


class TestTransactionErrors:
    """Test transaction-related error handling."""

    @pytest.mark.asyncio
    async def test_create_rollback_on_error(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test create operation rollback on error."""
        initial_count = await crud_ins.count(db_session)
        
        try:
            async with db_session.begin():
                data = ModelTest(name='rollback_test')
                await crud_ins.create_model(db_session, data)
                # Force an error
                raise Exception("Intentional rollback")
        except Exception:
            pass
        
        # Verify rollback worked
        final_count = await crud_ins.count(db_session)
        assert final_count == initial_count

    @pytest.mark.asyncio
    async def test_update_rollback_on_error(self, populated_db: list[Ins], db_session_factory, crud_ins: CRUDPlus[Ins]):
        """Test update operation rollback on error."""
        first_item = populated_db[0]
        original_name = first_item.name
        item_id = first_item.id

        try:
            async with db_session_factory() as session:
                async with session.begin():
                    await crud_ins.update_model(
                        session,
                        item_id,
                        {'name': 'should_rollback'}
                    )
                    # Force an error
                    raise Exception("Intentional rollback")
        except Exception:
            pass

        # Verify rollback worked using a fresh session
        async with db_session_factory() as session:
            item = await crud_ins.select_model(session, item_id)
            assert item.name == original_name

    @pytest.mark.asyncio
    async def test_delete_rollback_on_error(self, populated_db: list[Ins], db_session_factory, crud_ins: CRUDPlus[Ins]):
        """Test delete operation rollback on error."""
        first_item = populated_db[0]
        item_id = first_item.id

        # Get initial count
        async with db_session_factory() as session:
            initial_count = await crud_ins.count(session)

        try:
            async with db_session_factory() as session:
                async with session.begin():
                    await crud_ins.delete_model(session, item_id)
                    # Force an error
                    raise Exception("Intentional rollback")
        except Exception:
            pass

        # Verify rollback worked using fresh sessions
        async with db_session_factory() as session:
            final_count = await crud_ins.count(session)
            assert final_count == initial_count

            item_still_exists = await crud_ins.select_model(session, item_id)
            assert item_still_exists is not None


class TestDataValidationErrors:
    """Test data validation error scenarios."""

    @pytest.mark.asyncio
    async def test_create_with_invalid_data_type(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test creating with invalid data types."""
        # This test depends on the specific validation in your Pydantic models
        # Adjust based on your actual model constraints
        async with db_session.begin():
            # Test with valid data first to ensure the test setup is correct
            data = ModelTest(name='valid_test')
            result = await crud_ins.create_model(db_session, data)
            assert result.name == 'valid_test'

    @pytest.mark.asyncio
    async def test_update_with_invalid_data_type(self, populated_db: list[Ins], db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test updating with invalid data types."""
        first_item = populated_db[0]
        
        async with db_session.begin():
            # Test with valid data
            result = await crud_ins.update_model(
                db_session,
                first_item.id,
                {'name': 'valid_update'}
            )
            assert result == 1


class TestConcurrencyErrors:
    """Test concurrency-related error scenarios."""

    @pytest.mark.asyncio
    async def test_concurrent_update_same_record(self, populated_db: list[Ins], db_session_factory, crud_ins: CRUDPlus[Ins]):
        """Test concurrent updates to the same record."""
        first_item = populated_db[0]
        
        # This test verifies that concurrent operations don't cause issues
        # In a real scenario, you might want to test for specific concurrency controls
        
        async def update_operation(name_suffix: str):
            async with db_session_factory() as session:
                async with session.begin():
                    await crud_ins.update_model(
                        session,
                        first_item.id,
                        {'name': f'concurrent_update_{name_suffix}'}
                    )
        
        # Run concurrent updates
        import asyncio
        await asyncio.gather(
            update_operation('1'),
            update_operation('2')
        )
        
        # Verify one of the updates succeeded
        async with db_session_factory() as session:
            updated_item = await crud_ins.select_model(session, first_item.id)
            assert 'concurrent_update_' in updated_item.name
