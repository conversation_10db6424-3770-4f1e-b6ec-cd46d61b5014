#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Unit tests for CREATE operations in SQLAlchemy CRUD Plus.
"""
import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from sqlalchemy_crud_plus import CRUDPlus
from tests.model import Ins, InsPks
from tests.schema import ModelTest, ModelTestPks


class TestCreateSingleModel:
    """Test creating single model instances."""

    @pytest.mark.asyncio
    async def test_create_basic_model(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test creating a basic model with minimal data."""
        async with db_session.begin():
            data = ModelTest(name='basic_test')
            result = await crud_ins.create_model(db_session, data)
            await db_session.flush()
            
            assert result.name == 'basic_test'
            assert result.id is not None
            assert result.del_flag is False

    @pytest.mark.asyncio
    async def test_create_model_with_kwargs(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test creating a model with additional kwargs."""
        async with db_session.begin():
            data = ModelTest(name='kwargs_test')
            result = await crud_ins.create_model(db_session, data, del_flag=True)
            await db_session.flush()
            
            assert result.name == 'kwargs_test'
            assert result.del_flag is True

    @pytest.mark.asyncio
    async def test_create_model_with_flush(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test creating a model with flush=True."""
        async with db_session.begin():
            data = ModelTest(name='flush_test')
            result = await crud_ins.create_model(db_session, data, flush=True)
            
            assert result.name == 'flush_test'
            assert result.id is not None

    @pytest.mark.asyncio
    async def test_create_model_with_commit(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test creating a model with commit=True."""
        data = ModelTest(name='commit_test')
        result = await crud_ins.create_model(db_session, data, commit=True)
        
        assert result.name == 'commit_test'
        assert result.id is not None
        
        # Verify persistence
        count = await crud_ins.count(db_session, name='commit_test')
        assert count == 1

    @pytest.mark.asyncio
    async def test_create_model_special_characters(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test creating models with special characters."""
        special_names = [
            "test'quote",
            'test"double',
            'test\\backslash',
            'test%percent',
            '测试中文',
            '🚀 emoji'
        ]
        
        async with db_session.begin():
            for name in special_names:
                data = ModelTest(name=name)
                result = await crud_ins.create_model(db_session, data)
                await db_session.flush()
                assert result.name == name


class TestCreateMultipleModels:
    """Test creating multiple model instances."""

    @pytest.mark.asyncio
    async def test_create_multiple_basic(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins], sample_schemas: list[ModelTest]):
        """Test creating multiple models with basic data."""
        async with db_session.begin():
            results = await crud_ins.create_models(db_session, sample_schemas)
            await db_session.flush()
            
            assert len(results) == len(sample_schemas)
            for i, result in enumerate(results):
                assert result.name == f'test_item_{i + 1}'
                assert result.id is not None

    @pytest.mark.asyncio
    async def test_create_multiple_with_kwargs(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test creating multiple models with kwargs."""
        async with db_session.begin():
            test_data = [ModelTest(name=f'bulk_item_{i}') for i in range(1, 4)]
            results = await crud_ins.create_models(db_session, test_data, del_flag=True)
            await db_session.flush()
            
            assert len(results) == 3
            for result in results:
                assert result.del_flag is True

    @pytest.mark.asyncio
    async def test_create_multiple_with_flush(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test creating multiple models with flush=True."""
        async with db_session.begin():
            test_data = [ModelTest(name=f'flush_bulk_{i}') for i in range(1, 3)]
            results = await crud_ins.create_models(db_session, test_data, flush=True)
            
            assert len(results) == 2
            for result in results:
                assert result.id is not None

    @pytest.mark.asyncio
    async def test_create_multiple_with_commit(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test creating multiple models with commit=True."""
        test_data = [ModelTest(name=f'commit_bulk_{i}') for i in range(1, 3)]
        results = await crud_ins.create_models(db_session, test_data, commit=True)
        
        assert len(results) == 2
        
        # Verify persistence
        count = await crud_ins.count(db_session, name__startswith='commit_bulk_')
        assert count == 2

    @pytest.mark.asyncio
    async def test_create_empty_list(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test creating with empty list."""
        async with db_session.begin():
            results = await crud_ins.create_models(db_session, [])
            assert len(results) == 0

    @pytest.mark.asyncio
    async def test_create_large_batch(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test creating a large batch of models."""
        async with db_session.begin():
            batch_size = 100
            test_data = [ModelTest(name=f'large_batch_{i}') for i in range(batch_size)]
            results = await crud_ins.create_models(db_session, test_data)
            await db_session.flush()
            
            assert len(results) == batch_size
            for i, result in enumerate(results):
                assert result.name == f'large_batch_{i}'


class TestCreateCompositeKeyModels:
    """Test creating models with composite primary keys."""

    @pytest.mark.asyncio
    async def test_create_composite_key_basic(self, db_session: AsyncSession, crud_ins_pks: CRUDPlus[InsPks]):
        """Test creating a model with composite primary key."""
        async with db_session.begin():
            data = ModelTestPks(id=100, name='composite_test', sex='men')
            result = await crud_ins_pks.create_model(db_session, data)
            await db_session.flush()
            
            assert result.name == 'composite_test'
            assert result.id == 100
            assert result.sex == 'men'

    @pytest.mark.asyncio
    async def test_create_multiple_composite_keys(self, db_session: AsyncSession, crud_ins_pks: CRUDPlus[InsPks]):
        """Test creating multiple models with composite keys."""
        async with db_session.begin():
            test_data = [
                ModelTestPks(id=200, name='comp_test_1', sex='men'),
                ModelTestPks(id=201, name='comp_test_2', sex='women'),
                ModelTestPks(id=202, name='comp_test_3', sex='men')
            ]
            results = await crud_ins_pks.create_models(db_session, test_data)
            await db_session.flush()
            
            assert len(results) == 3
            assert results[0].sex == 'men'
            assert results[1].sex == 'women'
            assert results[2].sex == 'men'

    @pytest.mark.asyncio
    async def test_create_composite_key_with_kwargs(self, db_session: AsyncSession, crud_ins_pks: CRUDPlus[InsPks]):
        """Test creating composite key model with additional kwargs."""
        async with db_session.begin():
            data = ModelTestPks(id=300, name='comp_kwargs_test', sex='women')
            result = await crud_ins_pks.create_model(db_session, data, del_flag=True)
            await db_session.flush()
            
            assert result.name == 'comp_kwargs_test'
            assert result.del_flag is True


class TestCreateDataValidation:
    """Test data validation during create operations."""

    @pytest.mark.asyncio
    async def test_create_with_none_values(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test creating model with None values where allowed."""
        async with db_session.begin():
            # Test with valid None value for optional field
            data = ModelTest(name='none_test')
            result = await crud_ins.create_model(db_session, data, del_flag=None)
            await db_session.flush()
            
            assert result.name == 'none_test'
            # del_flag should have default value since None was passed

    @pytest.mark.asyncio
    async def test_create_with_empty_string(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test creating model with empty string."""
        async with db_session.begin():
            data = ModelTest(name='')
            result = await crud_ins.create_model(db_session, data)
            await db_session.flush()
            
            assert result.name == ''

    @pytest.mark.asyncio
    async def test_create_with_whitespace_string(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test creating model with whitespace-only string."""
        async with db_session.begin():
            data = ModelTest(name='   ')
            result = await crud_ins.create_model(db_session, data)
            await db_session.flush()
            
            assert result.name == '   '

    @pytest.mark.asyncio
    async def test_create_with_long_string(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test creating model with long string (within limits)."""
        async with db_session.begin():
            long_name = 'A' * 60  # Within the 64 character limit
            data = ModelTest(name=long_name)
            result = await crud_ins.create_model(db_session, data)
            await db_session.flush()
            
            assert result.name == long_name
            assert len(result.name) == 60


class TestCreatePerformanceOptimizations:
    """Test performance optimizations in create operations."""

    @pytest.mark.asyncio
    async def test_model_dump_optimization(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test that model_dump() is called only once per operation."""
        async with db_session.begin():
            # This test verifies the optimization where model_dump() is cached
            data = ModelTest(name='optimization_test')
            
            # Create with kwargs to test the optimization path
            result = await crud_ins.create_model(db_session, data, del_flag=True)
            await db_session.flush()
            
            assert result.name == 'optimization_test'
            assert result.del_flag is True

    @pytest.mark.asyncio
    async def test_bulk_create_optimization(self, db_session: AsyncSession, crud_ins: CRUDPlus[Ins]):
        """Test bulk creation optimization."""
        async with db_session.begin():
            # Test that bulk operations are optimized
            bulk_data = [ModelTest(name=f'bulk_opt_{i}') for i in range(10)]
            results = await crud_ins.create_models(db_session, bulk_data, del_flag=False)
            await db_session.flush()
            
            assert len(results) == 10
            for result in results:
                assert result.del_flag is False
