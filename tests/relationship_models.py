#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from datetime import datetime
from typing import Optional

from sqlalchemy import (
    <PERSON>olean, Column, DateTime, Foreign<PERSON>ey, Integer, String, Table, Text, UniqueConstraint
)
from sqlalchemy.orm import DeclarativeBase, Mapped, MappedAsDataclass, declared_attr, mapped_column, relationship


class RelationshipBase(MappedAsDataclass, DeclarativeBase):
    @declared_attr.directive
    def __tablename__(cls) -> str:
        return cls.__name__.lower()


# Association tables for many-to-many relationships
user_role_table = Table(
    'user_roles',
    RelationshipBase.metadata,
    Column('user_id', Integer, ForeignKey('user.id'), primary_key=True),
    <PERSON>umn('role_id', Integer, ForeignKey('role.id'), primary_key=True)
)

post_tag_table = Table(
    'post_tags',
    RelationshipBase.metadata,
    Column('post_id', Integer, <PERSON><PERSON><PERSON>('post.id'), primary_key=True),
    <PERSON>umn('tag_id', Integer, <PERSON><PERSON>ey('tag.id'), primary_key=True)
)


class User(RelationshipBase):
    __tablename__ = 'user'

    id: Mapped[int] = mapped_column(init=False, primary_key=True, index=True, autoincrement=True)
    username: Mapped[str] = mapped_column(String(50), unique=True, nullable=False)
    email: Mapped[str] = mapped_column(String(100), unique=True, nullable=False)
    full_name: Mapped[Optional[str]] = mapped_column(String(100), default=None)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True)
    created_at: Mapped[datetime] = mapped_column(DateTime, init=False, default_factory=datetime.utcnow)
    updated_at: Mapped[Optional[datetime]] = mapped_column(DateTime, init=False, onupdate=datetime.utcnow)

    # One-to-one relationship
    profile: Mapped[Optional["UserProfile"]] = relationship(
        "UserProfile", back_populates="user", uselist=False, cascade="all, delete-orphan", init=False
    )

    # One-to-many relationship
    posts: Mapped[list["Post"]] = relationship(
        "Post", back_populates="author", cascade="all, delete-orphan", init=False, default_factory=list
    )

    # One-to-many relationship (comments)
    comments: Mapped[list["Comment"]] = relationship(
        "Comment", back_populates="author", cascade="all, delete-orphan", init=False, default_factory=list
    )

    # Many-to-many relationship
    roles: Mapped[list["Role"]] = relationship(
        "Role", secondary=user_role_table, back_populates="users", init=False, default_factory=list
    )


class UserProfile(RelationshipBase):
    __tablename__ = 'userprofile'

    id: Mapped[int] = mapped_column(init=False, primary_key=True, index=True, autoincrement=True)
    user_id: Mapped[int] = mapped_column(Integer, ForeignKey('user.id'), unique=True, nullable=False)
    bio: Mapped[Optional[str]] = mapped_column(Text, default=None)
    avatar_url: Mapped[Optional[str]] = mapped_column(String(255), default=None)
    website: Mapped[Optional[str]] = mapped_column(String(255), default=None)
    location: Mapped[Optional[str]] = mapped_column(String(100), default=None)
    birth_date: Mapped[Optional[datetime]] = mapped_column(DateTime, default=None)
    created_at: Mapped[datetime] = mapped_column(DateTime, init=False, default_factory=datetime.utcnow)
    updated_at: Mapped[Optional[datetime]] = mapped_column(DateTime, init=False, onupdate=datetime.utcnow)

    # One-to-one relationship (back reference)
    user: Mapped["User"] = relationship("User", back_populates="profile", init=False)


class Category(RelationshipBase):
    __tablename__ = 'category'

    id: Mapped[int] = mapped_column(init=False, primary_key=True, index=True, autoincrement=True)
    name: Mapped[str] = mapped_column(String(100), unique=True, nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text, default=None)
    parent_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey('category.id'), default=None)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True)
    created_at: Mapped[datetime] = mapped_column(DateTime, init=False, default_factory=datetime.utcnow)
    updated_at: Mapped[Optional[datetime]] = mapped_column(DateTime, init=False, onupdate=datetime.utcnow)

    # Self-referential relationship
    parent: Mapped[Optional["Category"]] = relationship(
        "Category", remote_side=[id], back_populates="children", init=False
    )
    children: Mapped[list["Category"]] = relationship(
        "Category", back_populates="parent", cascade="all, delete-orphan", init=False, default_factory=list
    )

    # One-to-many relationship
    posts: Mapped[list["Post"]] = relationship(
        "Post", back_populates="category", init=False, default_factory=list
    )


class Post(RelationshipBase):
    __tablename__ = 'post'

    id: Mapped[int] = mapped_column(init=False, primary_key=True, index=True, autoincrement=True)
    title: Mapped[str] = mapped_column(String(200), nullable=False)
    content: Mapped[str] = mapped_column(Text, nullable=False)
    author_id: Mapped[int] = mapped_column(Integer, ForeignKey('user.id'), nullable=False)
    category_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey('category.id'), default=None)
    is_published: Mapped[bool] = mapped_column(Boolean, default=False)
    view_count: Mapped[int] = mapped_column(Integer, default=0)
    created_at: Mapped[datetime] = mapped_column(DateTime, init=False, default_factory=datetime.utcnow)
    updated_at: Mapped[Optional[datetime]] = mapped_column(DateTime, init=False, onupdate=datetime.utcnow)

    # Many-to-one relationship
    author: Mapped["User"] = relationship("User", back_populates="posts", init=False)
    category: Mapped[Optional["Category"]] = relationship("Category", back_populates="posts", init=False)

    # One-to-many relationship
    comments: Mapped[list["Comment"]] = relationship(
        "Comment", back_populates="post", cascade="all, delete-orphan", init=False, default_factory=list
    )

    # Many-to-many relationship
    tags: Mapped[list["Tag"]] = relationship(
        "Tag", secondary=post_tag_table, back_populates="posts", init=False, default_factory=list
    )


class Comment(RelationshipBase):
    __tablename__ = 'comment'

    id: Mapped[int] = mapped_column(init=False, primary_key=True, index=True, autoincrement=True)
    content: Mapped[str] = mapped_column(Text, nullable=False)
    author_id: Mapped[int] = mapped_column(Integer, ForeignKey('user.id'), nullable=False)
    post_id: Mapped[int] = mapped_column(Integer, ForeignKey('post.id'), nullable=False)
    parent_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey('comment.id'), default=None)
    is_approved: Mapped[bool] = mapped_column(Boolean, default=True)
    created_at: Mapped[datetime] = mapped_column(DateTime, init=False, default_factory=datetime.utcnow)
    updated_at: Mapped[Optional[datetime]] = mapped_column(DateTime, init=False, onupdate=datetime.utcnow)

    # Many-to-one relationships
    author: Mapped["User"] = relationship("User", back_populates="comments", init=False)
    post: Mapped["Post"] = relationship("Post", back_populates="comments", init=False)

    # Self-referential relationship for nested comments
    parent: Mapped[Optional["Comment"]] = relationship(
        "Comment", remote_side=[id], back_populates="replies", init=False
    )
    replies: Mapped[list["Comment"]] = relationship(
        "Comment", back_populates="parent", cascade="all, delete-orphan", init=False, default_factory=list
    )


class Role(RelationshipBase):
    __tablename__ = 'role'

    id: Mapped[int] = mapped_column(init=False, primary_key=True, index=True, autoincrement=True)
    name: Mapped[str] = mapped_column(String(50), unique=True, nullable=False)
    description: Mapped[Optional[str]] = mapped_column(String(200), default=None)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True)
    created_at: Mapped[datetime] = mapped_column(DateTime, init=False, default_factory=datetime.utcnow)

    # Many-to-many relationship (back reference)
    users: Mapped[list["User"]] = relationship(
        "User", secondary=user_role_table, back_populates="roles", init=False, default_factory=list
    )


class Tag(RelationshipBase):
    __tablename__ = 'tag'

    id: Mapped[int] = mapped_column(init=False, primary_key=True, index=True, autoincrement=True)
    name: Mapped[str] = mapped_column(String(50), unique=True, nullable=False)
    color: Mapped[Optional[str]] = mapped_column(String(7), default=None)  # Hex color
    is_active: Mapped[bool] = mapped_column(Boolean, default=True)
    created_at: Mapped[datetime] = mapped_column(DateTime, init=False, default_factory=datetime.utcnow)

    # Many-to-many relationship (back reference)
    posts: Mapped[list["Post"]] = relationship(
        "Post", secondary=post_tag_table, back_populates="tags", init=False, default_factory=list
    )


# Additional model for testing complex relationships
class Department(RelationshipBase):
    __tablename__ = 'department'

    id: Mapped[int] = mapped_column(init=False, primary_key=True, index=True, autoincrement=True)
    name: Mapped[str] = mapped_column(String(100), unique=True, nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text, default=None)
    manager_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey('employee.id'), default=None)
    created_at: Mapped[datetime] = mapped_column(DateTime, init=False, default_factory=datetime.utcnow)

    # One-to-one relationship (manager)
    manager: Mapped[Optional["Employee"]] = relationship(
        "Employee", foreign_keys=[manager_id], back_populates="managed_department", init=False
    )

    # One-to-many relationship (employees)
    employees: Mapped[list["Employee"]] = relationship(
        "Employee", foreign_keys="Employee.department_id", back_populates="department", init=False, default_factory=list
    )


class Employee(RelationshipBase):
    __tablename__ = 'employee'

    id: Mapped[int] = mapped_column(init=False, primary_key=True, index=True, autoincrement=True)
    name: Mapped[str] = mapped_column(String(100), nullable=False)
    email: Mapped[str] = mapped_column(String(100), unique=True, nullable=False)
    department_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey('department.id'), default=None)
    supervisor_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey('employee.id'), default=None)
    salary: Mapped[Optional[int]] = mapped_column(Integer, default=None)
    hire_date: Mapped[datetime] = mapped_column(DateTime, default_factory=datetime.utcnow)
    created_at: Mapped[datetime] = mapped_column(DateTime, init=False, default_factory=datetime.utcnow)

    # Many-to-one relationship (department)
    department: Mapped[Optional["Department"]] = relationship(
        "Department", foreign_keys=[department_id], back_populates="employees", init=False
    )

    # One-to-one relationship (managed department)
    managed_department: Mapped[Optional["Department"]] = relationship(
        "Department", foreign_keys="Department.manager_id", back_populates="manager", init=False
    )

    # Self-referential relationship (supervisor/subordinates)
    supervisor: Mapped[Optional["Employee"]] = relationship(
        "Employee", remote_side=[id], back_populates="subordinates", init=False
    )
    subordinates: Mapped[list["Employee"]] = relationship(
        "Employee", back_populates="supervisor", cascade="all, delete-orphan", init=False, default_factory=list
    )
