#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Comprehensive relationship models for testing SQLAlchemy CRUD Plus relationship support.
Includes all major relationship types: One-to-One, One-to-Many, Many-to-One, Many-to-Many, Self-referential.
"""
from datetime import datetime
from typing import List, Optional

from sqlalchemy import (
    Column, Integer, String, DateTime, Boolean, ForeignKey, Text, Table
)
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column, relationship


class RelationshipTestBase(DeclarativeBase):
    """Base class for relationship test models."""
    pass


# ==================== Many-to-Many Association Tables ====================

# User-Role association table for many-to-many relationship
user_role_association = Table(
    'user_roles',
    RelationshipTestBase.metadata,
    Column('user_id', Integer, ForeignKey('users.id'), primary_key=True),
    Column('role_id', Inte<PERSON>, Foreign<PERSON>ey('roles.id'), primary_key=True),
    <PERSON>umn('assigned_at', DateTime, default=datetime.utcnow)
)

# Post-Tag association table for many-to-many relationship
post_tag_association = Table(
    'post_tags',
    RelationshipTestBase.metadata,
    Column('post_id', Integer, ForeignKey('posts.id'), primary_key=True),
    Column('tag_id', Integer, ForeignKey('tags.id'), primary_key=True)
)


# ==================== Core Models ====================

class User(RelationshipTestBase):
    """User model demonstrating various relationship types."""
    __tablename__ = 'users'
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    username: Mapped[str] = mapped_column(String(50), unique=True, nullable=False)
    email: Mapped[str] = mapped_column(String(100), unique=True, nullable=False)
    full_name: Mapped[Optional[str]] = mapped_column(String(100))
    is_active: Mapped[bool] = mapped_column(Boolean, default=True)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    
    # One-to-One: User -> UserProfile
    profile: Mapped[Optional["UserProfile"]] = relationship(
        "UserProfile", 
        back_populates="user", 
        uselist=False,
        cascade="all, delete-orphan"
    )
    
    # One-to-Many: User -> Posts
    posts: Mapped[List["Post"]] = relationship(
        "Post", 
        back_populates="author",
        cascade="all, delete-orphan"
    )
    
    # One-to-Many: User -> Comments
    comments: Mapped[List["Comment"]] = relationship(
        "Comment", 
        back_populates="author",
        cascade="all, delete-orphan"
    )
    
    # Many-to-Many: User <-> Role
    roles: Mapped[List["Role"]] = relationship(
        "Role",
        secondary=user_role_association,
        back_populates="users"
    )
    
    # Self-referential: User -> User (followers/following)
    following: Mapped[List["User"]] = relationship(
        "User",
        secondary="user_followers",
        primaryjoin="User.id == user_followers.c.follower_id",
        secondaryjoin="User.id == user_followers.c.followed_id",
        back_populates="followers"
    )
    
    followers: Mapped[List["User"]] = relationship(
        "User",
        secondary="user_followers",
        primaryjoin="User.id == user_followers.c.followed_id",
        secondaryjoin="User.id == user_followers.c.follower_id",
        back_populates="following"
    )


class UserProfile(RelationshipTestBase):
    """User profile model for one-to-one relationship."""
    __tablename__ = 'user_profiles'
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    user_id: Mapped[int] = mapped_column(Integer, ForeignKey('users.id'), unique=True)
    bio: Mapped[Optional[str]] = mapped_column(Text)
    avatar_url: Mapped[Optional[str]] = mapped_column(String(255))
    birth_date: Mapped[Optional[datetime]] = mapped_column(DateTime)
    location: Mapped[Optional[str]] = mapped_column(String(100))
    website: Mapped[Optional[str]] = mapped_column(String(255))
    
    # One-to-One: UserProfile -> User
    user: Mapped["User"] = relationship("User", back_populates="profile")


class Category(RelationshipTestBase):
    """Category model for hierarchical relationships."""
    __tablename__ = 'categories'
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    name: Mapped[str] = mapped_column(String(100), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text)
    parent_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey('categories.id'))
    is_active: Mapped[bool] = mapped_column(Boolean, default=True)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    
    # Self-referential: Category -> Category (parent/children)
    parent: Mapped[Optional["Category"]] = relationship(
        "Category", 
        remote_side=[id],
        back_populates="children"
    )
    
    children: Mapped[List["Category"]] = relationship(
        "Category",
        back_populates="parent",
        cascade="all, delete-orphan"
    )
    
    # One-to-Many: Category -> Posts
    posts: Mapped[List["Post"]] = relationship(
        "Post", 
        back_populates="category"
    )


class Post(RelationshipTestBase):
    """Post model demonstrating multiple relationship types."""
    __tablename__ = 'posts'
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    title: Mapped[str] = mapped_column(String(200), nullable=False)
    content: Mapped[str] = mapped_column(Text, nullable=False)
    author_id: Mapped[int] = mapped_column(Integer, ForeignKey('users.id'))
    category_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey('categories.id'))
    is_published: Mapped[bool] = mapped_column(Boolean, default=False)
    published_at: Mapped[Optional[datetime]] = mapped_column(DateTime)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[Optional[datetime]] = mapped_column(DateTime, onupdate=datetime.utcnow)
    
    # Many-to-One: Post -> User
    author: Mapped["User"] = relationship("User", back_populates="posts")
    
    # Many-to-One: Post -> Category
    category: Mapped[Optional["Category"]] = relationship("Category", back_populates="posts")
    
    # One-to-Many: Post -> Comments
    comments: Mapped[List["Comment"]] = relationship(
        "Comment", 
        back_populates="post",
        cascade="all, delete-orphan"
    )
    
    # Many-to-Many: Post <-> Tag
    tags: Mapped[List["Tag"]] = relationship(
        "Tag",
        secondary=post_tag_association,
        back_populates="posts"
    )


class Comment(RelationshipTestBase):
    """Comment model for nested relationships."""
    __tablename__ = 'comments'
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    content: Mapped[str] = mapped_column(Text, nullable=False)
    author_id: Mapped[int] = mapped_column(Integer, ForeignKey('users.id'))
    post_id: Mapped[int] = mapped_column(Integer, ForeignKey('posts.id'))
    parent_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey('comments.id'))
    is_approved: Mapped[bool] = mapped_column(Boolean, default=True)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    
    # Many-to-One: Comment -> User
    author: Mapped["User"] = relationship("User", back_populates="comments")
    
    # Many-to-One: Comment -> Post
    post: Mapped["Post"] = relationship("Post", back_populates="comments")
    
    # Self-referential: Comment -> Comment (parent/replies)
    parent: Mapped[Optional["Comment"]] = relationship(
        "Comment", 
        remote_side=[id],
        back_populates="replies"
    )
    
    replies: Mapped[List["Comment"]] = relationship(
        "Comment",
        back_populates="parent",
        cascade="all, delete-orphan"
    )


class Role(RelationshipTestBase):
    """Role model for many-to-many relationship."""
    __tablename__ = 'roles'
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    name: Mapped[str] = mapped_column(String(50), unique=True, nullable=False)
    description: Mapped[Optional[str]] = mapped_column(String(200))
    is_active: Mapped[bool] = mapped_column(Boolean, default=True)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    
    # Many-to-Many: Role <-> User
    users: Mapped[List["User"]] = relationship(
        "User",
        secondary=user_role_association,
        back_populates="roles"
    )


class Tag(RelationshipTestBase):
    """Tag model for many-to-many relationship."""
    __tablename__ = 'tags'
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    name: Mapped[str] = mapped_column(String(50), unique=True, nullable=False)
    color: Mapped[Optional[str]] = mapped_column(String(7))  # Hex color code
    is_active: Mapped[bool] = mapped_column(Boolean, default=True)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    
    # Many-to-Many: Tag <-> Post
    posts: Mapped[List["Post"]] = relationship(
        "Post",
        secondary=post_tag_association,
        back_populates="tags"
    )


# Self-referential association table for user followers
user_followers = Table(
    'user_followers',
    RelationshipTestBase.metadata,
    Column('follower_id', Integer, ForeignKey('users.id'), primary_key=True),
    Column('followed_id', Integer, ForeignKey('users.id'), primary_key=True),
    Column('followed_at', DateTime, default=datetime.utcnow)
)
