#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Comprehensive tests for SQLAlchemy CRUD Plus covering edge cases and advanced scenarios.
"""
import pytest
import asyncio
from datetime import datetime, timedelta

from sqlalchemy_crud_plus import CRUDPlus
from sqlalchemy_crud_plus.errors import MultipleResultsError, ModelColumnError
from tests.model import Ins, InsPks
from tests.schema import ModelTest, ModelTestPks


class TestComprehensiveFeatures:
    """Comprehensive tests for advanced features and edge cases."""

    @pytest.mark.asyncio
    async def test_unicode_and_special_characters(self, async_db_session):
        """Test handling of Unicode and special characters."""
        async with async_db_session.begin() as session:
            crud = CRUDPlus(Ins)
            
            # Test various Unicode and special characters
            test_cases = [
                '测试中文',  # Chinese
                'тест',  # Cyrillic
                'test\'quote',  # Single quote
                'test"double',  # Double quote
                'test%percent',  # Percent
                'test_underscore',  # Underscore
                'test\\backslash',  # Backslash
                '🚀 emoji',  # Emoji
            ]
            
            created_ids = []
            for name in test_cases:
                data = ModelTest(name=name)
                record = await crud.create_model(session, data)
                created_ids.append(record.id)
                await session.flush()  # Ensure record is available
            
            # Verify all records can be retrieved
            for i, record_id in enumerate(created_ids):
                retrieved = await crud.select_model(session, record_id)
                assert retrieved is not None
                assert retrieved.name == test_cases[i]

    @pytest.mark.asyncio
    async def test_complex_filter_combinations(self, async_db_session):
        """Test complex filter combinations."""
        async with async_db_session.begin() as session:
            crud = CRUDPlus(Ins)
            
            # Create test data
            test_data = [
                ('filter_test_001', True),
                ('filter_test_002', False),
                ('filter_test_003', True),
                ('other_test_001', False),
                ('other_test_002', True),
            ]
            
            for name, del_flag in test_data:
                data = ModelTest(name=name)
                await crud.create_model(session, data, del_flag=del_flag)
            
            # Test complex AND conditions
            results = await crud.select_models(
                session,
                name__startswith='filter_test_',
                del_flag=True
            )
            assert len(results) == 2  # filter_test_001 and filter_test_003
            
            # Test IN operator with other conditions
            results = await crud.select_models(
                session,
                name__in=['filter_test_001', 'other_test_001'],
                del_flag__in=[True, False]
            )
            assert len(results) == 2
            
            # Test NOT operations
            results = await crud.select_models(
                session,
                name__not_like='%other%',
                del_flag=True
            )
            # Should include filter_test_001 and filter_test_003
            assert len(results) >= 2

    @pytest.mark.asyncio
    async def test_or_filter_scenarios(self, async_db_session):
        """Test OR filter scenarios."""
        async with async_db_session.begin() as session:
            crud = CRUDPlus(Ins)
            
            # Create test data
            test_names = ['or_test_1', 'or_test_2', 'different_name']
            for name in test_names:
                data = ModelTest(name=name)
                await crud.create_model(session, data)
            
            # Test OR with multiple conditions
            results = await crud.select_models(
                session,
                name__or={
                    'eq': 'or_test_1',
                    'like': '%test_2%',
                    'startswith': 'different_'
                }
            )
            assert len(results) == 3
            
            # Test nested OR conditions
            results = await crud.select_models(
                session,
                __or__=[
                    {'name__eq': 'or_test_1'},
                    {'name__like': '%test_2%'}
                ]
            )
            assert len(results) == 2

    @pytest.mark.asyncio
    async def test_arithmetic_operations(self, async_db_session):
        """Test arithmetic operations in filters."""
        async with async_db_session.begin() as session:
            crud = CRUDPlus(Ins)
            
            # Create records with predictable IDs
            records = []
            for i in range(1, 6):  # Create 5 records
                data = ModelTest(name=f'arith_test_{i}')
                record = await crud.create_model(session, data)
                records.append(record)
            
            # Get the actual IDs
            record_ids = [r.id for r in records]
            
            # Test addition: find record where id + 1 equals the second record's ID
            if len(record_ids) >= 2:
                target_id = record_ids[1]  # Second record's ID
                results = await crud.select_models(
                    session,
                    id__add={'value': 1, 'condition': {'eq': target_id}},
                    name__startswith='arith_test_'
                )
                # Should find the first record (first_id + 1 = second_id)
                assert len(results) >= 0  # May be 0 if IDs are not sequential
            
            # Test multiplication: id * 2 > some value
            if record_ids:
                min_id = min(record_ids)
                results = await crud.select_models(
                    session,
                    id__mul={'value': 2, 'condition': {'gt': min_id * 2}},
                    name__startswith='arith_test_'
                )
                assert len(results) >= 0  # Should work without error

    @pytest.mark.asyncio
    async def test_sorting_complex_scenarios(self, async_db_session):
        """Test complex sorting scenarios."""
        async with async_db_session.begin() as session:
            crud = CRUDPlus(Ins)
            
            # Create test data with specific sorting challenges
            test_data = [
                'sort_10_item',
                'sort_2_item',
                'sort_1_item',
                'Sort_Capital',
                'sort_lowercase',
            ]
            
            for name in test_data:
                data = ModelTest(name=name)
                await crud.create_model(session, data)
            
            # Test single column sorting
            results = await crud.select_models_order(
                session,
                sort_columns='name',
                sort_orders='asc',
                name__contains='sort'
            )
            
            # Verify sorting order
            names = [r.name for r in results]
            assert names == sorted(names)
            
            # Test multi-column sorting
            results = await crud.select_models_order(
                session,
                sort_columns=['name', 'id'],
                sort_orders=['desc', 'asc'],
                name__contains='sort'
            )
            
            # Should be sorted by name DESC, then by id ASC
            assert len(results) >= len(test_data) - 1

    @pytest.mark.asyncio
    async def test_bulk_operations_with_conditions(self, async_db_session):
        """Test bulk operations with complex conditions."""
        async with async_db_session.begin() as session:
            crud = CRUDPlus(Ins)
            
            # Create test data
            categories = ['A', 'B']
            for category in categories:
                for i in range(5):
                    data = ModelTest(name=f'bulk_{category}_{i:02d}')
                    await crud.create_model(session, data, del_flag=(i % 2 == 0))
            
            # Bulk update with complex conditions
            update_count = await crud.update_model_by_column(
                session,
                {'name': 'bulk_updated'},
                allow_multiple=True,
                name__like='bulk_A_%',
                del_flag=False
            )
            # Should update records: bulk_A_01, bulk_A_03 (del_flag=False)
            assert update_count >= 2
            
            # Verify updates
            updated_records = await crud.select_models(
                session,
                name='bulk_updated'
            )
            assert len(updated_records) >= 2
            
            # Bulk logical deletion
            delete_count = await crud.delete_model_by_column(
                session,
                allow_multiple=True,
                logical_deletion=True,
                name__like='bulk_B_%',
                del_flag=False
            )
            assert delete_count >= 2

    @pytest.mark.asyncio
    async def test_exists_optimization(self, async_db_session):
        """Test exists vs count optimization."""
        async with async_db_session.begin() as session:
            crud = CRUDPlus(Ins)
            
            # Create test data
            for i in range(10):
                data = ModelTest(name=f'exists_test_{i}')
                await crud.create_model(session, data)
        
        async with async_db_session() as session:
            crud = CRUDPlus(Ins)
            
            # Test exists for existing data
            exists_result = await crud.exists(session, name__startswith='exists_test_')
            assert exists_result is True
            
            # Test exists for non-existing data
            exists_result = await crud.exists(session, name__startswith='nonexistent_')
            assert exists_result is False
            
            # Test count for comparison
            count_result = await crud.count(session, name__startswith='exists_test_')
            assert count_result == 10
            
            # Test exists with complex conditions
            exists_complex = await crud.exists(
                session,
                name__like='exists_test_1%',
                del_flag=False
            )
            assert exists_complex is True

    @pytest.mark.asyncio
    async def test_update_with_different_data_types(self, async_db_session):
        """Test updates with various data types."""
        async with async_db_session.begin() as session:
            crud = CRUDPlus(Ins)
            
            # Create initial record
            data = ModelTest(name='update_test')
            record = await crud.create_model(session, data)
            await session.flush()
            
            # Test updating with dictionary
            dict_update = {
                'name': 'updated_via_dict',
                'del_flag': True
            }
            result = await crud.update_model(session, record.id, dict_update)
            assert result == 1
            
            # Verify update
            updated = await crud.select_model(session, record.id)
            assert updated.name == 'updated_via_dict'
            assert updated.del_flag is True
            
            # Test updating with Pydantic model
            pydantic_update = ModelTest(name='updated_via_pydantic')
            result = await crud.update_model(session, record.id, pydantic_update)
            assert result == 1
            
            # Verify update
            updated = await crud.select_model(session, record.id)
            assert updated.name == 'updated_via_pydantic'

    @pytest.mark.asyncio
    async def test_error_handling_and_validation(self, async_db_session):
        """Test error handling and input validation."""
        crud = CRUDPlus(Ins)
        
        async with async_db_session.begin() as session:
            # Test update without filters
            with pytest.raises(ValueError, match="At least one filter condition"):
                await crud.update_model_by_column(session, {'name': 'test'})
            
            # Test delete without filters
            with pytest.raises(ValueError, match="At least one filter condition"):
                await crud.delete_model_by_column(session)
            
            # Test logical deletion with non-existent column
            with pytest.raises(ModelColumnError):
                await crud.delete_model_by_column(
                    session,
                    logical_deletion=True,
                    deleted_flag_column='nonexistent_column',
                    name='test'
                )

    @pytest.mark.asyncio
    async def test_composite_key_operations(self, create_test_model_pks, async_db_session):
        """Test operations with composite primary keys."""
        async with async_db_session.begin() as session:
            crud = CRUDPlus(InsPks)
            
            # Test bulk operations with composite keys
            update_data = {'name': 'bulk_updated_composite'}
            
            # Update multiple records by column filter
            result = await crud.update_model_by_column(
                session,
                update_data,
                allow_multiple=True,
                sex='men'
            )
            assert result >= 0  # May be 0 if no men records exist
            
            # Test exists with composite key conditions
            exists_result = await crud.exists(session, sex='men')
            # Should not raise an error

    @pytest.mark.asyncio
    async def test_datetime_operations(self, async_db_session):
        """Test datetime operations."""
        async with async_db_session.begin() as session:
            crud = CRUDPlus(Ins)
            
            # Create a record
            data = ModelTest(name='datetime_test')
            record = await crud.create_model(session, data)
            await session.flush()
            
            # Test datetime range queries
            now = datetime.now()
            yesterday = now - timedelta(days=1)
            tomorrow = now + timedelta(days=1)
            
            # Query records created in a time range
            results = await crud.select_models(
                session,
                created_time__between=[yesterday, tomorrow],
                name='datetime_test'
            )
            assert len(results) >= 1
            
            # Query records created before yesterday (should be empty for our record)
            results = await crud.select_models(
                session,
                created_time__lt=yesterday,
                name='datetime_test'
            )
            assert len(results) == 0

    @pytest.mark.asyncio
    async def test_concurrent_operations(self, async_db_session):
        """Test concurrent operations."""
        # Create initial data
        async with async_db_session.begin() as session:
            crud = CRUDPlus(Ins)
            for i in range(5):
                data = ModelTest(name=f'concurrent_test_{i}')
                await crud.create_model(session, data)
        
        async def read_operation(session_factory, filter_value: int):
            """Perform a read operation."""
            async with session_factory() as session:
                crud = CRUDPlus(Ins)
                return await crud.select_models(
                    session, 
                    name=f'concurrent_test_{filter_value}'
                )
        
        # Test concurrent reads
        tasks = [
            read_operation(async_db_session, i) 
            for i in range(3)
        ]
        results = await asyncio.gather(*tasks)
        
        # Verify results
        assert len(results) == 3
        for result in results:
            assert len(result) <= 1  # Each should find 0 or 1 record

    @pytest.mark.asyncio
    async def test_empty_and_null_values(self, async_db_session):
        """Test handling of empty and null values."""
        async with async_db_session.begin() as session:
            crud = CRUDPlus(Ins)
            
            # Test empty string
            empty_data = ModelTest(name='')
            empty_record = await crud.create_model(session, empty_data)
            await session.flush()
            assert empty_record.name == ''
            
            # Test retrieval of empty string
            found_empty = await crud.select_model(session, empty_record.id)
            assert found_empty is not None
            assert found_empty.name == ''
            
            # Test whitespace string
            whitespace_data = ModelTest(name='   ')
            whitespace_record = await crud.create_model(session, whitespace_data)
            await session.flush()
            assert whitespace_record.name == '   '
            
            # Test NULL-like operations (should not crash)
            null_results = await crud.select_models(session, name__is=None)
            assert isinstance(null_results, list)
            
            not_null_results = await crud.select_models(session, name__is_not=None)
            assert len(not_null_results) >= 2  # At least our test records
