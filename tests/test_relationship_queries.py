#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import pytest
import pytest_asyncio
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload, joinedload

from sqlalchemy_crud_plus import CRUDPlus
from tests.relationship_models import User, UserProfile, Post, Category, Role


class TestBasicRelationshipQueries:
    """Test basic relationship loading with options."""

    async def test_select_with_selectinload(self, relationship_session: AsyncSession, user_crud: CRUDPlus[User], sample_data):
        """Test selectinload strategy for one-to-many relationships."""
        users = await user_crud.select_models(
            relationship_session,
            options=[selectinload(User.posts)],
            is_active=True
        )

        assert len(users) == 3
        # Check that posts are loaded
        for user in users:
            assert hasattr(user, 'posts')
            # Posts should be loaded without additional queries
            posts_count = len(user.posts)
            assert posts_count >= 0

    async def test_select_with_joinedload(self, relationship_session: AsyncSession, user_crud: CRUDPlus[User], sample_data):
        """Test joinedload strategy for one-to-one relationships."""
        users = await user_crud.select_models(
            relationship_session,
            options=[joinedload(User.profile)]
        )

        assert len(users) == 3
        # Check that profiles are loaded for users who have them
        users_with_profiles = [u for u in users if u.profile is not None]
        assert len(users_with_profiles) == 2  # Only first 2 users have profiles

    async def test_select_model_with_joins(self, relationship_session: AsyncSession, user_crud: CRUDPlus[User], sample_data):
        """Test select_model with joins parameter."""
        user_id = sample_data['users'][0].id

        user = await user_crud.select_model(
            relationship_session,
            user_id,
            joins=[{'target': 'posts', 'type': 'left'}],
            options=[selectinload(User.posts)]
        )

        assert user is not None
        assert user.id == user_id
        assert hasattr(user, 'posts')

    async def test_select_models_with_relationships_method(self, relationship_session: AsyncSession, user_crud: CRUDPlus[User], sample_data):
        """Test the new select_models_with_relationships method."""
        users = await user_crud.select_models_with_relationships(
            relationship_session,
            relationships=['posts', 'profile', 'roles'],
            strategy='selectinload'
        )

        assert len(users) == 3
        # Verify all relationships are loaded
        for user in users:
            assert hasattr(user, 'posts')
            assert hasattr(user, 'profile')
            assert hasattr(user, 'roles')

    async def test_select_model_with_relationships_method(self, relationship_session: AsyncSession, user_crud: CRUDPlus[User], sample_data):
        """Test the new select_model_with_relationships method."""
        user_id = sample_data['users'][0].id

        user = await user_crud.select_model_with_relationships(
            relationship_session,
            user_id,
            relationships=['posts', 'roles'],
            strategy='selectinload'
        )

        assert user is not None
        assert user.id == user_id
        assert hasattr(user, 'posts')
        assert hasattr(user, 'roles')


class TestAdvancedJoinQueries:
    """Test advanced join functionality."""

    async def test_inner_join_query(self, relationship_session: AsyncSession, post_crud: CRUDPlus[Post], sample_data):
        """Test inner join with relationship."""
        posts = await post_crud.select_models(
            relationship_session,
            joins=[{'target': 'author', 'type': 'inner'}],
            options=[selectinload(Post.author)],
            is_published=True
        )

        # Should return posts that have authors (all posts should have authors)
        assert len(posts) > 0
        for post in posts:
            assert post.author is not None
            assert post.is_published is True

    async def test_left_join_query(self, relationship_session: AsyncSession, post_crud: CRUDPlus[Post], sample_data):
        """Test left outer join with relationship."""
        posts = await post_crud.select_models(
            relationship_session,
            joins=[{'target': 'category', 'type': 'left'}],
            options=[selectinload(Post.category)]
        )

        # Should return all posts, including those without categories
        assert len(posts) == 5
        posts_with_category = [p for p in posts if p.category is not None]
        assert len(posts_with_category) > 0

    async def test_multiple_joins(self, relationship_session: AsyncSession, post_crud: CRUDPlus[Post], sample_data):
        """Test multiple joins in one query."""
        posts = await post_crud.select_models(
            relationship_session,
            joins=[
                {'target': 'author', 'type': 'inner'},
                {'target': 'category', 'type': 'left'}
            ],
            options=[
                selectinload(Post.author),
                selectinload(Post.category)
            ]
        )

        assert len(posts) > 0
        for post in posts:
            assert post.author is not None  # Inner join ensures author exists
            # Category may be None due to left join


class TestRelationshipFiltering:
    """Test filtering across relationships."""

    async def test_filter_by_relationship_field(self, relationship_session: AsyncSession, post_crud: CRUDPlus[Post], sample_data):
        """Test filtering posts by author username."""
        posts = await post_crud.select_models(
            relationship_session,
            options=[selectinload(Post.author)],
            author__username='user_1'
        )

        assert len(posts) > 0
        for post in posts:
            assert post.author.username == 'user_1'

    async def test_filter_by_relationship_field_with_operator(self, relationship_session: AsyncSession, post_crud: CRUDPlus[Post], sample_data):
        """Test filtering with operators across relationships."""
        posts = await post_crud.select_models(
            relationship_session,
            options=[selectinload(Post.author)],
            author__username__like='user_%'
        )

        assert len(posts) > 0
        for post in posts:
            assert post.author.username.startswith('user_')

    async def test_complex_relationship_filtering(self, relationship_session: AsyncSession, user_crud: CRUDPlus[User], sample_data):
        """Test complex filtering across multiple relationships."""
        users = await user_crud.select_models(
            relationship_session,
            options=[selectinload(User.posts)],
            posts__is_published=True,
            posts__view_count__gt=10
        )

        # Should return users who have published posts with view_count > 10
        assert len(users) >= 0


class TestSelfReferencingRelationships:
    """Test self-referencing relationships."""

    async def test_category_parent_child(self, relationship_session: AsyncSession, category_crud: CRUDPlus[Category], sample_data):
        """Test querying categories with parent-child relationships."""
        categories = await category_crud.select_models(
            relationship_session,
            options=[selectinload(Category.children), selectinload(Category.parent)]
        )

        assert len(categories) == 3

        # Find parent categories (those without parent_id)
        parent_categories = [c for c in categories if c.parent_id is None]
        assert len(parent_categories) == 2  # Technology and Science

        # Find child categories
        child_categories = [c for c in categories if c.parent_id is not None]
        assert len(child_categories) == 1  # Programming


class TestManyToManyRelationships:
    """Test many-to-many relationships."""

    async def test_user_roles_relationship(self, relationship_session: AsyncSession, user_crud: CRUDPlus[User], sample_data):
        """Test user-roles many-to-many relationship."""
        users = await user_crud.select_models(
            relationship_session,
            options=[selectinload(User.roles)]
        )

        assert len(users) == 3

        # Check that some users have roles
        users_with_roles = [u for u in users if len(u.roles) > 0]
        assert len(users_with_roles) >= 2  # At least 2 users have roles

    async def test_filter_by_many_to_many_relationship(self, relationship_session: AsyncSession, user_crud: CRUDPlus[User], sample_data):
        """Test filtering by many-to-many relationship."""
        users = await user_crud.select_models(
            relationship_session,
            options=[selectinload(User.roles)],
            roles__name='admin'
        )

        # Should return users with admin role
        assert len(users) >= 1
        for user in users:
            role_names = [role.name for role in user.roles]
            assert 'admin' in role_names


class TestAggregationQueries:
    """Test aggregation queries with relationships."""

    async def test_count_with_joins(self, relationship_session: AsyncSession, post_crud: CRUDPlus[Post], sample_data):
        """Test counting with joins."""
        count = await post_crud.count_with_joins(
            relationship_session,
            joins=[{'target': 'author', 'type': 'inner'}],
            is_published=True
        )

        assert count >= 0
        assert isinstance(count, int)

    async def test_aggregation_query(self, relationship_session: AsyncSession, post_crud: CRUDPlus[Post], sample_data):
        """Test aggregation queries."""
        results = await post_crud.aggregate(
            relationship_session,
            aggregations={
                'total_posts': 'count',
                'avg_views': ('avg', 'view_count'),
                'max_views': ('max', 'view_count'),
                'min_views': ('min', 'view_count')
            },
            is_published=True
        )

        assert len(results) == 1
        result = results[0]
        assert 'total_posts' in result
        assert 'avg_views' in result
        assert 'max_views' in result
        assert 'min_views' in result
        assert result['total_posts'] >= 0

    async def test_aggregation_with_group_by(self, relationship_session: AsyncSession, post_crud: CRUDPlus[Post], sample_data):
        """Test aggregation with group by."""
        results = await post_crud.aggregate(
            relationship_session,
            aggregations={
                'post_count': 'count',
                'avg_views': ('avg', 'view_count')
            },
            group_by=['author_id'],
            joins=[{'target': 'author', 'type': 'inner'}]
        )

        assert len(results) > 0
        for result in results:
            assert 'post_count' in result
            assert 'avg_views' in result
            assert 'author_id' in result


class TestEnhancedSelectMethods:
    """Test enhanced select methods with relationship support."""

    async def test_select_models_order_with_joins(self, relationship_session: AsyncSession, post_crud: CRUDPlus[Post], sample_data):
        """Test select_models_order with joins and options."""
        posts = await post_crud.select_models_order(
            relationship_session,
            sort_columns=['created_time'],
            sort_orders=['desc'],
            joins=[{'target': 'author', 'type': 'inner'}],
            options=[selectinload(Post.author)],
            is_published=True
        )

        assert len(posts) >= 0
        # Verify sorting (newer posts first)
        if len(posts) > 1:
            for i in range(len(posts) - 1):
                assert posts[i].created_time >= posts[i + 1].created_time

    async def test_select_model_by_column_with_joins(self, relationship_session: AsyncSession, post_crud: CRUDPlus[Post], sample_data):
        """Test select_model_by_column with joins."""
        post = await post_crud.select_model_by_column(
            relationship_session,
            joins=[{'target': 'author', 'type': 'inner'}],
            options=[selectinload(Post.author)],
            is_published=True
        )

        if post:
            assert post.is_published is True
            assert hasattr(post, 'author')
            assert post.author is not None


class TestErrorHandling:
    """Test error handling in relationship queries."""

    async def test_invalid_relationship_name_in_options(self, relationship_session: AsyncSession, user_crud: CRUDPlus[User]):
        """Test error handling for invalid relationship names in options."""
        # This should handle gracefully or raise appropriate error
        try:
            users = await user_crud.select_models(
                relationship_session,
                options=[selectinload('nonexistent_relationship')]
            )
            # If no error is raised, the result should be empty or handle gracefully
            assert isinstance(users, (list, tuple))
        except Exception as e:
            # Should be a specific relationship error
            assert 'relationship' in str(e).lower() or 'attribute' in str(e).lower()

    async def test_invalid_join_configuration(self, relationship_session: AsyncSession, post_crud: CRUDPlus[Post]):
        """Test error handling for invalid join configuration."""
        # This should handle gracefully or raise appropriate error
        try:
            posts = await post_crud.select_models(
                relationship_session,
                joins=[{'target': 'nonexistent_relationship', 'type': 'inner'}]
            )
            # If no error is raised, the result should be empty or handle gracefully
            assert isinstance(posts, (list, tuple))
        except Exception as e:
            # Should be a specific relationship error
            assert 'relationship' in str(e).lower() or 'join' in str(e).lower()


@pytest_asyncio.fixture
async def populated_relationship_db(db_session: AsyncSession) -> dict:
    """Provide a database populated with relationship test data."""
    async with db_session.begin():
        # Create users
        users = [
            User(username=f'user_{i}', email=f'user{i}@example.com', full_name=f'User {i}')
            for i in range(1, 6)
        ]
        db_session.add_all(users)
        await db_session.flush()

        # Create user profiles
        profiles = [
            UserProfile(user_id=users[i].id, bio=f'Bio for user {i+1}', location=f'City {i+1}')
            for i in range(3)  # Only first 3 users have profiles
        ]
        db_session.add_all(profiles)

        # Create categories
        categories = [
            Category(name='Technology', description='Tech related posts'),
            Category(name='Science', description='Science related posts'),
            Category(name='Programming', description='Programming tutorials', parent_id=1),  # Child of Technology
        ]
        db_session.add_all(categories)
        await db_session.flush()

        # Create posts
        posts = [
            Post(title=f'Post {i}', content=f'Content of post {i}', 
                 author_id=users[i % len(users)].id, 
                 category_id=categories[i % len(categories)].id,
                 is_published=i % 2 == 0, view_count=i * 10)
            for i in range(1, 11)
        ]
        db_session.add_all(posts)
        await db_session.flush()

        # Create comments
        comments = [
            Comment(content=f'Comment {i}', author_id=users[i % len(users)].id, 
                   post_id=posts[i % len(posts)].id, is_approved=i % 3 != 0)
            for i in range(1, 16)
        ]
        # Add some nested comments
        comments.extend([
            Comment(content='Reply 1', author_id=users[0].id, post_id=posts[0].id, parent_id=1),
            Comment(content='Reply 2', author_id=users[1].id, post_id=posts[0].id, parent_id=1),
        ])
        db_session.add_all(comments)
        await db_session.flush()

        # Create roles
        roles = [
            Role(name='admin', description='Administrator role'),
            Role(name='editor', description='Editor role'),
            Role(name='viewer', description='Viewer role'),
        ]
        db_session.add_all(roles)
        await db_session.flush()

        # Create tags
        tags = [
            Tag(name='python', color='#3776ab'),
            Tag(name='javascript', color='#f7df1e'),
            Tag(name='database', color='#336791'),
            Tag(name='web', color='#61dafb'),
        ]
        db_session.add_all(tags)
        await db_session.flush()

        # Create departments and employees
        departments = [
            Department(name='Engineering', description='Software development'),
            Department(name='Marketing', description='Marketing and sales'),
        ]
        db_session.add_all(departments)
        await db_session.flush()

        employees = [
            Employee(name='John Doe', email='<EMAIL>', department_id=departments[0].id, salary=80000),
            Employee(name='Jane Smith', email='<EMAIL>', department_id=departments[0].id, salary=75000),
            Employee(name='Bob Wilson', email='<EMAIL>', department_id=departments[1].id, salary=65000),
        ]
        # Set supervisor relationship
        employees[1].supervisor_id = employees[0].id  # Jane reports to John
        db_session.add_all(employees)
        await db_session.flush()

        # Set department manager
        departments[0].manager_id = employees[0].id  # John manages Engineering
        await db_session.flush()

        # Create many-to-many relationships
        # User-Role relationships
        await db_session.execute(
            user_role_table.insert().values([
                {'user_id': users[0].id, 'role_id': roles[0].id},  # user1 is admin
                {'user_id': users[1].id, 'role_id': roles[1].id},  # user2 is editor
                {'user_id': users[2].id, 'role_id': roles[2].id},  # user3 is viewer
                {'user_id': users[0].id, 'role_id': roles[1].id},  # user1 is also editor
            ])
        )

        # Post-Tag relationships
        await db_session.execute(
            post_tag_table.insert().values([
                {'post_id': posts[0].id, 'tag_id': tags[0].id},  # post1 has python tag
                {'post_id': posts[0].id, 'tag_id': tags[2].id},  # post1 has database tag
                {'post_id': posts[1].id, 'tag_id': tags[1].id},  # post2 has javascript tag
                {'post_id': posts[2].id, 'tag_id': tags[0].id},  # post3 has python tag
                {'post_id': posts[2].id, 'tag_id': tags[3].id},  # post3 has web tag
            ])
        )

        await db_session.commit()

    return {
        'users': users,
        'profiles': profiles,
        'categories': categories,
        'posts': posts,
        'comments': comments,
        'roles': roles,
        'tags': tags,
        'departments': departments,
        'employees': employees,
    }


# CRUD instances for testing
@pytest.fixture
def user_crud() -> CRUDPlus[User]:
    return CRUDPlus(User)


@pytest.fixture
def post_crud() -> CRUDPlus[Post]:
    return CRUDPlus(Post)


@pytest.fixture
def category_crud() -> CRUDPlus[Category]:
    return CRUDPlus(Category)


class TestBasicRelationshipQueries:
    """Test basic relationship loading with options."""

    async def test_select_with_selectinload(self, db_session: AsyncSession, user_crud: CRUDPlus[User], populated_relationship_db):
        """Test selectinload strategy for one-to-many relationships."""
        users = await user_crud.select_models(
            db_session,
            options=[selectinload(User.posts)],
            is_active=True
        )
        
        assert len(users) == 5
        # Check that posts are loaded
        for user in users:
            assert hasattr(user, 'posts')
            # Posts should be loaded without additional queries
            posts_count = len(user.posts)
            assert posts_count >= 0

    async def test_select_with_joinedload(self, db_session: AsyncSession, user_crud: CRUDPlus[User], populated_relationship_db):
        """Test joinedload strategy for one-to-one relationships."""
        users = await user_crud.select_models(
            db_session,
            options=[joinedload(User.profile)],
            limit=3
        )
        
        assert len(users) <= 3
        # Check that profiles are loaded for users who have them
        users_with_profiles = [u for u in users if u.profile is not None]
        assert len(users_with_profiles) <= 3

    async def test_select_with_multiple_options(self, db_session: AsyncSession, user_crud: CRUDPlus[User], populated_relationship_db):
        """Test multiple loading strategies in one query."""
        users = await user_crud.select_models(
            db_session,
            options=[
                selectinload(User.posts),
                joinedload(User.profile),
                selectinload(User.roles)
            ]
        )
        
        assert len(users) == 5
        # Verify all relationships are loaded
        for user in users:
            assert hasattr(user, 'posts')
            assert hasattr(user, 'profile')
            assert hasattr(user, 'roles')


class TestAdvancedJoinQueries:
    """Test advanced join functionality."""

    async def test_inner_join_query(self, db_session: AsyncSession, post_crud: CRUDPlus[Post], populated_relationship_db):
        """Test inner join with relationship."""
        posts = await post_crud.query_with_joins(
            db_session,
            inner_joins=['author'],
            options=[selectinload(Post.author)],
            is_published=True
        )
        
        # Should return posts that have authors (all posts should have authors)
        assert len(posts) > 0
        for post in posts:
            assert post.author is not None
            assert post.is_published is True

    async def test_left_join_query(self, db_session: AsyncSession, post_crud: CRUDPlus[Post], populated_relationship_db):
        """Test left outer join with relationship."""
        posts = await post_crud.query_with_joins(
            db_session,
            left_joins=['category'],
            options=[selectinload(Post.category)]
        )
        
        # Should return all posts, including those without categories
        assert len(posts) == 10
        posts_with_category = [p for p in posts if p.category is not None]
        assert len(posts_with_category) > 0

    async def test_multiple_joins(self, db_session: AsyncSession, post_crud: CRUDPlus[Post], populated_relationship_db):
        """Test multiple joins in one query."""
        posts = await post_crud.query_with_joins(
            db_session,
            inner_joins=['author'],
            left_joins=['category'],
            options=[
                selectinload(Post.author),
                selectinload(Post.category),
                selectinload(Post.comments)
            ]
        )
        
        assert len(posts) > 0
        for post in posts:
            assert post.author is not None  # Inner join ensures author exists
            # Category may be None due to left join


class TestRelationshipFiltering:
    """Test filtering across relationships."""

    async def test_filter_by_relationship_field(self, db_session: AsyncSession, post_crud: CRUDPlus[Post], populated_relationship_db):
        """Test filtering posts by author username."""
        posts = await post_crud.select_models(
            db_session,
            options=[selectinload(Post.author)],
            author__username='user_1'
        )

        assert len(posts) > 0
        for post in posts:
            assert post.author.username == 'user_1'

    async def test_filter_by_relationship_field_with_operator(self, db_session: AsyncSession, post_crud: CRUDPlus[Post], populated_relationship_db):
        """Test filtering with operators across relationships."""
        posts = await post_crud.select_models(
            db_session,
            options=[selectinload(Post.author)],
            author__username__like='user_%'
        )

        assert len(posts) > 0
        for post in posts:
            assert post.author.username.startswith('user_')

    async def test_filter_by_nested_relationship(self, db_session: AsyncSession, comment_crud: CRUDPlus[Comment], populated_relationship_db):
        """Test filtering comments by post author."""
        comments = await comment_crud.select_models(
            db_session,
            options=[selectinload(Comment.post), selectinload(Comment.author)],
            post__author__username='user_1'
        )

        assert len(comments) >= 0
        # Verify the filtering worked correctly
        for comment in comments:
            # Note: We need to load the post's author to verify
            pass

    async def test_complex_relationship_filtering(self, db_session: AsyncSession, user_crud: CRUDPlus[User], populated_relationship_db):
        """Test complex filtering across multiple relationships."""
        users = await user_crud.select_models(
            db_session,
            options=[selectinload(User.posts)],
            posts__is_published=True,
            posts__view_count__gt=20
        )

        # Should return users who have published posts with view_count > 20
        assert len(users) >= 0


class TestSelfReferencingRelationships:
    """Test self-referencing relationships."""

    async def test_category_parent_child(self, db_session: AsyncSession, category_crud: CRUDPlus[Category], populated_relationship_db):
        """Test querying categories with parent-child relationships."""
        categories = await category_crud.select_models(
            db_session,
            options=[selectinload(Category.children), selectinload(Category.parent)]
        )

        assert len(categories) == 3

        # Find parent categories (those without parent_id)
        parent_categories = [c for c in categories if c.parent_id is None]
        assert len(parent_categories) == 2  # Technology and Science

        # Find child categories
        child_categories = [c for c in categories if c.parent_id is not None]
        assert len(child_categories) == 1  # Programming

    async def test_employee_supervisor_hierarchy(self, db_session: AsyncSession, employee_crud: CRUDPlus[Employee], populated_relationship_db):
        """Test employee supervisor hierarchy."""
        employees = await employee_crud.select_models(
            db_session,
            options=[selectinload(Employee.supervisor), selectinload(Employee.subordinates)]
        )

        assert len(employees) == 3

        # Find supervisors (those with subordinates)
        supervisors = [e for e in employees if len(e.subordinates) > 0]
        assert len(supervisors) >= 1

        # Find employees with supervisors
        supervised_employees = [e for e in employees if e.supervisor_id is not None]
        assert len(supervised_employees) >= 1


class TestManyToManyRelationships:
    """Test many-to-many relationships."""

    async def test_user_roles_relationship(self, db_session: AsyncSession, user_crud: CRUDPlus[User], populated_relationship_db):
        """Test user-roles many-to-many relationship."""
        users = await user_crud.select_models(
            db_session,
            options=[selectinload(User.roles)]
        )

        assert len(users) == 5

        # Check that some users have roles
        users_with_roles = [u for u in users if len(u.roles) > 0]
        assert len(users_with_roles) >= 3

    async def test_post_tags_relationship(self, db_session: AsyncSession, post_crud: CRUDPlus[Post], populated_relationship_db):
        """Test post-tags many-to-many relationship."""
        posts = await post_crud.select_models(
            db_session,
            options=[selectinload(Post.tags)]
        )

        assert len(posts) == 10

        # Check that some posts have tags
        posts_with_tags = [p for p in posts if len(p.tags) > 0]
        assert len(posts_with_tags) >= 3

    async def test_filter_by_many_to_many_relationship(self, db_session: AsyncSession, user_crud: CRUDPlus[User], populated_relationship_db):
        """Test filtering by many-to-many relationship."""
        users = await user_crud.select_models(
            db_session,
            options=[selectinload(User.roles)],
            roles__name='admin'
        )

        # Should return users with admin role
        assert len(users) >= 1
        for user in users:
            role_names = [role.name for role in user.roles]
            assert 'admin' in role_names


class TestAggregationQueries:
    """Test aggregation queries with relationships."""

    async def test_count_with_joins(self, db_session: AsyncSession, post_crud: CRUDPlus[Post], populated_relationship_db):
        """Test counting with joins."""
        count = await post_crud.count_with_joins(
            db_session,
            joins=[{'target': 'author', 'type': 'inner'}],
            is_published=True
        )

        assert count >= 0
        assert isinstance(count, int)

    async def test_aggregation_query(self, db_session: AsyncSession, post_crud: CRUDPlus[Post], populated_relationship_db):
        """Test aggregation queries."""
        results = await post_crud.aggregate(
            db_session,
            aggregations={
                'total_posts': 'count',
                'avg_views': ('avg', 'view_count'),
                'max_views': ('max', 'view_count'),
                'min_views': ('min', 'view_count')
            },
            is_published=True
        )

        assert len(results) == 1
        result = results[0]
        assert 'total_posts' in result
        assert 'avg_views' in result
        assert 'max_views' in result
        assert 'min_views' in result
        assert result['total_posts'] >= 0

    async def test_aggregation_with_group_by(self, db_session: AsyncSession, post_crud: CRUDPlus[Post], populated_relationship_db):
        """Test aggregation with group by."""
        results = await post_crud.aggregate(
            db_session,
            aggregations={
                'post_count': 'count',
                'avg_views': ('avg', 'view_count')
            },
            group_by=['author_id'],
            joins=[{'target': 'author', 'type': 'inner'}]
        )

        assert len(results) > 0
        for result in results:
            assert 'post_count' in result
            assert 'avg_views' in result
            assert 'author_id' in result


class TestAdvancedQueryMethods:
    """Test advanced query methods."""

    async def test_query_method_with_pagination(self, db_session: AsyncSession, post_crud: CRUDPlus[Post], populated_relationship_db):
        """Test query method with pagination."""
        posts = await post_crud.query(
            db_session,
            options=[selectinload(Post.author)],
            sort_columns=['created_at'],
            sort_orders=['desc'],
            limit=5,
            offset=0
        )

        assert len(posts) <= 5
        # Verify sorting (newer posts first)
        if len(posts) > 1:
            for i in range(len(posts) - 1):
                assert posts[i].created_at >= posts[i + 1].created_at

    async def test_query_one_method(self, db_session: AsyncSession, post_crud: CRUDPlus[Post], populated_relationship_db):
        """Test query_one method."""
        post = await post_crud.query_one(
            db_session,
            options=[selectinload(Post.author), selectinload(Post.category)],
            is_published=True
        )

        if post:
            assert post.is_published is True
            assert hasattr(post, 'author')
            assert hasattr(post, 'category')

    async def test_query_with_relationships_method(self, db_session: AsyncSession, user_crud: CRUDPlus[User], populated_relationship_db):
        """Test query_with_relationships method."""
        users = await user_crud.query_with_relationships(
            db_session,
            relationships=['posts', 'profile', 'roles'],
            strategy='selectinload',
            limit=3
        )

        assert len(users) <= 3
        for user in users:
            assert hasattr(user, 'posts')
            assert hasattr(user, 'profile')
            assert hasattr(user, 'roles')


class TestErrorHandling:
    """Test error handling in relationship queries."""

    async def test_invalid_relationship_name(self, db_session: AsyncSession, user_crud: CRUDPlus[User]):
        """Test error handling for invalid relationship names."""
        with pytest.raises(Exception):  # Should raise RelationshipError
            await user_crud.select_models(
                db_session,
                options=[selectinload(User.nonexistent_relationship)]
            )

    async def test_invalid_join_configuration(self, db_session: AsyncSession, post_crud: CRUDPlus[Post]):
        """Test error handling for invalid join configuration."""
        # This should handle gracefully or raise appropriate error
        try:
            posts = await post_crud.query(
                db_session,
                joins=[{'target': 'nonexistent_relationship', 'type': 'inner'}]
            )
            # If no error is raised, the result should be empty or handle gracefully
            assert isinstance(posts, (list, tuple))
        except Exception as e:
            # Should be a specific relationship error
            assert 'relationship' in str(e).lower() or 'join' in str(e).lower()
