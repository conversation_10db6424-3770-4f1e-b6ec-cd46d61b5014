#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Syntax check for the relationship functionality without importing SQLAlchemy.
This script checks for Python syntax errors and basic code structure.
"""
import ast
import os
import sys


def check_python_syntax(file_path):
    """Check if a Python file has valid syntax."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Parse the AST to check syntax
        ast.parse(content)
        return True, None
    except SyntaxError as e:
        return False, f"Syntax error: {e}"
    except Exception as e:
        return False, f"Error reading file: {e}"


def check_file_structure():
    """Check the structure of our modified files."""
    files_to_check = [
        'sqlalchemy_crud_plus/crud.py',
        'sqlalchemy_crud_plus/utils.py', 
        'sqlalchemy_crud_plus/errors.py',
        'tests/relationship_models.py',
        'tests/relationship_schemas.py',
        'tests/test_relationships.py',
        'examples/blog_example.py'
    ]
    
    print("🔍 Checking Python syntax for modified files...")
    print("=" * 50)
    
    all_valid = True
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            is_valid, error = check_python_syntax(file_path)
            if is_valid:
                print(f"✅ {file_path} - Syntax OK")
            else:
                print(f"❌ {file_path} - {error}")
                all_valid = False
        else:
            print(f"⚠️  {file_path} - File not found")
    
    return all_valid


def check_method_definitions():
    """Check that new methods are properly defined in CRUDPlus."""
    crud_file = 'sqlalchemy_crud_plus/crud.py'
    
    if not os.path.exists(crud_file):
        print(f"❌ {crud_file} not found")
        return False
    
    print("\n🔍 Checking CRUDPlus method definitions...")
    print("=" * 50)
    
    try:
        with open(crud_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Parse AST to find method definitions
        tree = ast.parse(content)
        
        # Find CRUDPlus class
        crud_class = None
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef) and node.name == 'CRUDPlus':
                crud_class = node
                break
        
        if not crud_class:
            print("❌ CRUDPlus class not found")
            return False
        
        # Check for new relationship methods
        expected_methods = [
            'select_model_with_relationships',
            'select_model_by_column_with_relationships',
            'select_models_with_relationships', 
            'create_model_with_relationships',
            'update_model_with_relationships',
            'add_relationship',
            'remove_relationship'
        ]
        
        found_methods = []
        for node in crud_class.body:
            if isinstance(node, ast.AsyncFunctionDef):
                found_methods.append(node.name)
        
        all_found = True
        for method in expected_methods:
            if method in found_methods:
                print(f"✅ {method} - Method defined")
            else:
                print(f"❌ {method} - Method missing")
                all_found = False
        
        return all_found
        
    except Exception as e:
        print(f"❌ Error checking method definitions: {e}")
        return False


def check_utility_functions():
    """Check that utility functions are properly defined."""
    utils_file = 'sqlalchemy_crud_plus/utils.py'
    
    if not os.path.exists(utils_file):
        print(f"❌ {utils_file} not found")
        return False
    
    print("\n🔍 Checking utility function definitions...")
    print("=" * 50)
    
    try:
        with open(utils_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Parse AST to find function definitions
        tree = ast.parse(content)
        
        # Check for new utility functions
        expected_functions = [
            'parse_relationship_options',
            'get_relationship_attribute',
            'get_relationship_target_model',
            'validate_relationship_data',
            'build_relationship_filters'
        ]
        
        found_functions = []
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                found_functions.append(node.name)
        
        all_found = True
        for function in expected_functions:
            if function in found_functions:
                print(f"✅ {function} - Function defined")
            else:
                print(f"❌ {function} - Function missing")
                all_found = False
        
        return all_found
        
    except Exception as e:
        print(f"❌ Error checking utility functions: {e}")
        return False


def check_error_classes():
    """Check that error classes are properly defined."""
    errors_file = 'sqlalchemy_crud_plus/errors.py'
    
    if not os.path.exists(errors_file):
        print(f"❌ {errors_file} not found")
        return False
    
    print("\n🔍 Checking error class definitions...")
    print("=" * 50)
    
    try:
        with open(errors_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check if RelationshipError is defined
        if 'class RelationshipError' in content:
            print("✅ RelationshipError - Class defined")
            return True
        else:
            print("❌ RelationshipError - Class missing")
            return False
        
    except Exception as e:
        print(f"❌ Error checking error classes: {e}")
        return False


def main():
    """Run all syntax and structure checks."""
    print("🚀 SQLAlchemy CRUD Plus Relationship Code Structure Check")
    print("=" * 60)
    
    # Check syntax
    syntax_ok = check_file_structure()
    
    # Check method definitions
    methods_ok = check_method_definitions()
    
    # Check utility functions
    utils_ok = check_utility_functions()
    
    # Check error classes
    errors_ok = check_error_classes()
    
    print("\n" + "=" * 60)
    print("📊 SUMMARY:")
    print(f"  Syntax Check: {'✅ PASS' if syntax_ok else '❌ FAIL'}")
    print(f"  Method Definitions: {'✅ PASS' if methods_ok else '❌ FAIL'}")
    print(f"  Utility Functions: {'✅ PASS' if utils_ok else '❌ FAIL'}")
    print(f"  Error Classes: {'✅ PASS' if errors_ok else '❌ FAIL'}")
    
    if syntax_ok and methods_ok and utils_ok and errors_ok:
        print("\n🎉 All checks passed! The relationship functionality is properly structured.")
        print("\n📝 Next steps:")
        print("  1. Install dependencies: pip install sqlalchemy pydantic aiosqlite")
        print("  2. Run functional tests: python test_relationships_simple.py")
        print("  3. Run full test suite: pytest tests/test_relationships.py")
        return 0
    else:
        print("\n❌ Some checks failed. Please review the code structure.")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
