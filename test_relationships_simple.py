#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple test script to verify relationship functionality without pytest.
This script can be run directly to test the relationship features.
"""
import asyncio
import sys
import os

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from sqlalchemy import Column, Integer, String, ForeignKey, create_engine
    from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
    from sqlalchemy.orm import DeclarativeBase, relationship
    from pydantic import BaseModel
    
    from sqlalchemy_crud_plus import CRUDPlus
    
    print("✅ All imports successful!")
    
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Please install required dependencies:")
    print("pip install sqlalchemy pydantic aiosqlite")
    sys.exit(1)


# Simple test models
class Base(DeclarativeBase):
    pass


class Author(Base):
    __tablename__ = 'authors'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)
    email = Column(String(100), unique=True)
    
    # One-to-many relationship
    books = relationship("Book", back_populates="author")


class Book(Base):
    __tablename__ = 'books'
    
    id = Column(Integer, primary_key=True)
    title = Column(String(200), nullable=False)
    author_id = Column(Integer, ForeignKey('authors.id'))
    
    # Many-to-one relationship
    author = relationship("Author", back_populates="books")


# Pydantic schemas
class AuthorCreate(BaseModel):
    name: str
    email: str


class BookCreate(BaseModel):
    title: str


async def test_basic_relationships():
    """Test basic relationship functionality."""
    print("\n🧪 Testing basic relationships...")
    
    # Create in-memory SQLite database
    engine = create_async_engine("sqlite+aiosqlite:///:memory:", echo=False)
    async_session = async_sessionmaker(engine, class_=AsyncSession)
    
    # Create tables
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    async with async_session() as session:
        author_crud = CRUDPlus(Author)
        book_crud = CRUDPlus(Book)
        
        try:
            # Test 1: Create author
            print("  📝 Creating author...")
            author_data = AuthorCreate(name="Jane Doe", email="<EMAIL>")
            author = await author_crud.create_model(session, author_data)
            print(f"  ✅ Created author: {author.name} (ID: {author.id})")
            
            # Test 2: Create books for author
            print("  📚 Creating books...")
            book_titles = ["Python Guide", "SQLAlchemy Mastery", "Async Programming"]
            
            for title in book_titles:
                book_data = BookCreate(title=title)
                book = await book_crud.create_model(session, book_data, author_id=author.id)
                print(f"  ✅ Created book: {book.title} (ID: {book.id})")
            
            # Test 3: Query author with books using relationships
            print("  🔍 Querying author with books...")
            author_with_books = await author_crud.select_model_with_relationships(
                session, author.id, relationships="books"
            )
            
            if author_with_books and author_with_books.books:
                print(f"  ✅ Found author with {len(author_with_books.books)} books:")
                for book in author_with_books.books:
                    print(f"    - {book.title}")
            else:
                print("  ❌ Failed to load author with books")
                return False
            
            # Test 4: Query books with author
            print("  🔍 Querying books with author...")
            books_with_author = await book_crud.select_models_with_relationships(
                session, relationships="author"
            )
            
            if books_with_author:
                print(f"  ✅ Found {len(books_with_author)} books with author info:")
                for book in books_with_author:
                    print(f"    - '{book.title}' by {book.author.name}")
            else:
                print("  ❌ Failed to load books with author")
                return False
            
            # Test 5: Filter by relationship
            print("  🔍 Testing relationship filtering...")
            jane_books = await book_crud.select_models_with_relationships(
                session,
                relationships="author",
                author__name="Jane Doe"
            )
            
            if jane_books and len(jane_books) == 3:
                print(f"  ✅ Found {len(jane_books)} books by Jane Doe")
            else:
                print(f"  ❌ Expected 3 books by Jane Doe, found {len(jane_books) if jane_books else 0}")
                return False
            
            await session.commit()
            print("  ✅ All relationship tests passed!")
            return True
            
        except Exception as e:
            await session.rollback()
            print(f"  ❌ Test failed with error: {e}")
            import traceback
            traceback.print_exc()
            return False


async def test_loading_strategies():
    """Test different loading strategies."""
    print("\n🧪 Testing loading strategies...")
    
    engine = create_async_engine("sqlite+aiosqlite:///:memory:", echo=False)
    async_session = async_sessionmaker(engine, class_=AsyncSession)
    
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    async with async_session() as session:
        author_crud = CRUDPlus(Author)
        book_crud = CRUDPlus(Book)
        
        try:
            # Create test data
            author = await author_crud.create_model(
                session, AuthorCreate(name="Test Author", email="<EMAIL>")
            )
            
            for i in range(3):
                await book_crud.create_model(
                    session, BookCreate(title=f"Book {i+1}"), author_id=author.id
                )
            
            # Test selectinload (default)
            print("  🔄 Testing selectinload strategy...")
            author_selectin = await author_crud.select_model_with_relationships(
                session, author.id, relationships="books", loading_strategy="selectinload"
            )
            
            if author_selectin and len(author_selectin.books) == 3:
                print("  ✅ selectinload strategy works")
            else:
                print("  ❌ selectinload strategy failed")
                return False
            
            # Test joinedload
            print("  🔄 Testing joinedload strategy...")
            author_joined = await author_crud.select_model_with_relationships(
                session, author.id, relationships="books", loading_strategy="joinedload"
            )
            
            if author_joined and len(author_joined.books) == 3:
                print("  ✅ joinedload strategy works")
            else:
                print("  ❌ joinedload strategy failed")
                return False
            
            await session.commit()
            print("  ✅ All loading strategy tests passed!")
            return True
            
        except Exception as e:
            await session.rollback()
            print(f"  ❌ Loading strategy test failed: {e}")
            import traceback
            traceback.print_exc()
            return False


async def main():
    """Run all tests."""
    print("🚀 Starting SQLAlchemy CRUD Plus Relationship Tests")
    print("=" * 50)
    
    try:
        # Test basic relationships
        basic_test_passed = await test_basic_relationships()
        
        # Test loading strategies
        strategy_test_passed = await test_loading_strategies()
        
        print("\n" + "=" * 50)
        if basic_test_passed and strategy_test_passed:
            print("🎉 All tests passed! Relationship functionality is working correctly.")
            return 0
        else:
            print("❌ Some tests failed. Please check the implementation.")
            return 1
            
    except Exception as e:
        print(f"❌ Test suite failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
